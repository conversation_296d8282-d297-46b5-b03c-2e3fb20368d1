from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import Optional
from app.model import models, schemas
from app.database import database
from pydantic import BaseModel
import os
from http import HTTPStatus
from dashscope import Application
from fastapi.responses import StreamingResponse
import json
from fastapi import Request
import requests
from datetime import datetime, timezone, timedelta
import logging
import uuid
from fastapi.responses import PlainTextResponse
# apikey = os.getenv('DASHSCOPE_API_KEY')
apikey = "sk-ca59b3b0f14a4b4597c9f239cbb892b4"
router = APIRouter()

# dny123.ai & dny123相关能力

class Chat(BaseModel):
    appid: str
    session_id: Optional[str] = None
    prompt: str
    stream: bool = True
    openid: Optional[str] = None
    
@router.post("/chat")
async def chat(params: Chat, request: Request, db: Session = Depends(database.get_db)):
    # 获取客户端IP地址
    client_ip = request.client.host
    is_guest = False
    
    # 检查用户权限
    if params.openid:
        wechat_user = db.query(models.WechatUser).filter(models.WechatUser.openid == params.openid).first()
        if not wechat_user:
            # 用户不存在，走游客模式
            is_guest = True
        else:
            # 检查是否是VIP用户
            if not wechat_user.isvip:
                # 非VIP用户检查使用次数限制
                today = datetime.now(timezone.utc).date()
                last_use_date = wechat_user.subscribe_time.date() if wechat_user.subscribe_time else None
                
                # 如果是新的一天，重置计数
                if last_use_date != today:
                    wechat_user.todaycount = 30  # 重置为每日剩余30次
                    wechat_user.subscribe_time = datetime.now(timezone.utc)
                
                # 检查是否超过每日限制
                if wechat_user.todaycount <= 0:
                    return {"error": "今日使用次数已达上限", "code": 429}
                
                # 减少剩余使用次数
                wechat_user.todaycount -= 1
                db.commit()
    else:
        # 没有提供openid，走游客模式
        is_guest = True
    
    # 游客模式处理
    if is_guest:
        # 查找或创建游客记录
        guest_user = db.query(models.GuestUser).filter(models.GuestUser.ip_address == client_ip).first()
        
        if not guest_user:
            # 创建新游客记录
            guest_user = models.GuestUser(
                ip_address=client_ip,
                todaycount=20,
                subscribe_time=datetime.now(timezone.utc)
            )
            db.add(guest_user)
        else:
            # 检查是否需要重置每日次数
            today = datetime.now(timezone.utc).date()
            last_use_date = guest_user.subscribe_time.date() if guest_user.subscribe_time else None
            
            if last_use_date != today:
                guest_user.todaycount = 20  # 重置为每日剩余20次
                guest_user.subscribe_time = datetime.now(timezone.utc)
        
        # 检查游客是否超过每日限制
        if guest_user.todaycount <= 0:
            return {"error": "游客模式今日使用次数已达上限，请登录获取更多次数", "code": 429}
        
        # 减少游客剩余使用次数
        guest_user.todaycount -= 1
        db.commit()
    
    if params.stream:
        async def generate():
            try:
                response = Application.call(
                    api_key=apikey,
                    app_id=params.appid,
                    prompt=params.prompt,
                    session_id=params.session_id if params.session_id else None,
                    stream=True,
                    incremental_output=True
                )
                
                for chunk in response:
                    # 检查客户端连接状态
                    if await request.is_disconnected():
                        break
                        
                    if chunk.status_code != HTTPStatus.OK:
                        error_msg = {
                            'request_id': chunk.request_id,
                            'code': chunk.status_code,
                            'message': chunk.message,
                            'session_id': params.session_id
                        }
                        yield f"data: {json.dumps(error_msg)}\n\n"
                    else:
                        response_data = {
                            'text': chunk.output.text,
                            'session_id':  chunk.output.session_id,
                            "todaycount": guest_user.todaycount if is_guest else wechat_user.todaycount,
                            "is_guest": is_guest
                        }
                        yield f"data: {json.dumps(response_data)}\n\n"
                        
            except Exception as e:
                error_response = {
                    'error': str(e),
                    'session_id': params.session_id
                }
                yield f"data: {json.dumps(error_response)}\n\n"
                
        return StreamingResponse(generate(), media_type="text/event-stream")
    
    else:
        try:
            response = Application.call(
                api_key=apikey,
                app_id=params.appid,
                prompt=params.prompt,
                session_id=params.session_id if params.session_id else None,
                stream=False
            )
            
            if response.status_code != HTTPStatus.OK:
                return {
                    'request_id': response.request_id,
                    'code': response.status_code,
                    'message': response.message,
                    'session_id': params.session_id
                }
            
            return {
                'text': response.output.text,
                'session_id': response.output.session_id,
                'todaycount': guest_user.todaycount if is_guest else wechat_user.todaycount,
                'is_guest': is_guest
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'session_id': params.session_id
            }
        

# 微信公众号配置改成anhe
WECHAT_APPID = os.getenv("WECHAT_APPID", "wxa018ca4fa3fde5cc")
WECHAT_SECRET = os.getenv("WECHAT_SECRET", "eb8814a383c5d13d15423e10baad91aa")
WECHAT_TOKEN = os.getenv("WECHAT_TOKEN", "zfty123")  # 用于验证消息的合法性
WECHAT_EncodingAESKey = os.getenv("WECHAT_EncodingAESKey", "aJcf4thQ1NyEfi4zcrbqaf6eJxaYFQ2Qn1kUAyzvOjM")

# 获取微信access_token
def get_wechat_access_token(db: Session):
    # 从数据库或缓存中获取token，如果过期则重新获取
    # 这里简化处理，实际应用中应该缓存token
    url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={WECHAT_APPID}&secret={WECHAT_SECRET}"
    print(url)
    response = requests.get(url)
    data = response.json()
    print(data)
    if "access_token" in data:
        return data["access_token"]
    else:
        raise HTTPException(status_code=500, detail="获取微信access_token失败")

# 创建临时二维码
@router.post("/login/qrcode", response_model=schemas.WechatLoginResponse)
async def create_login_qrcode(db: Session = Depends(database.get_db)):
    access_token = get_wechat_access_token(db)
    
    # 生成唯一的场景值
    scene_str = str(uuid.uuid4())
    
    # 创建临时二维码票据
    url = f"https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token={access_token}"
    data = {
        "expire_seconds": 1800,  # 30分钟过期
        "action_name": "QR_STR_SCENE",
        "action_info": {"scene": {"scene_str": f"login_{scene_str}"}}
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if "ticket" not in result:
        raise HTTPException(status_code=500, detail="创建二维码失败")
    
    # 保存二维码信息到数据库
    expire_time = datetime.now(timezone.utc) + timedelta(seconds=1800)
    qrcode_db = models.QRCodeTicket(
        scene_str=scene_str,
        ticket=result["ticket"],
        url=result["url"],
        expire_time=expire_time
    )
    
    db.add(qrcode_db)
    db.commit()
    db.refresh(qrcode_db)
    
    # 返回二维码信息
    qrcode_url = f"https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket={result['ticket']}"
    return {
        "qrcode_url": qrcode_url,
        "scene_str": scene_str,
        "expire_time": expire_time
    }

# 检查登录状态
@router.get("/login/check/{scene_str}")
async def check_login_status(scene_str: str, db: Session = Depends(database.get_db)):
    qrcode = db.query(models.QRCodeTicket).filter(
        models.QRCodeTicket.scene_str == scene_str,
        models.QRCodeTicket.is_used == True,
        models.QRCodeTicket.expire_time > datetime.now(timezone.utc)
    ).first()
    
    if not qrcode:
        return {"status": "waiting"}
    
    # 查找关联的微信用户
    wechat_user = db.query(models.WechatUser).filter(
        models.WechatUser.openid == qrcode.openid
    ).first()
    
    if not wechat_user:
        return {"status": "failed"}
    
    # 如果微信用户未关联系统用户，则需要绑定或注册
    return {
        "status": "need_bind",
        "openid": wechat_user.openid,
        "isvip": wechat_user.isvip,
        "todaycount": wechat_user.todaycount
    }

# 微信服务器配置验证和消息接收
@router.get("/callback")
async def wechat_callback_verification(
    request: Request,
    signature: Optional[str] = None, 
    timestamp: Optional[str] = None, 
    nonce: Optional[str] = None, 
    echostr: Optional[str] = None,
):
    
    # 打印所有收到的参数
    print("GET /callback 收到的参数:")
    print(f"signature: {signature}")
    print(f"timestamp: {timestamp}")
    print(f"nonce: {nonce}")
    print(f"echostr: {echostr}")
    
    # 打印所有查询参数
    query_params = dict(request.query_params)
    print(f"所有查询参数: {query_params}")
    
    # 透传请求到目标地址
    target_url = "https://www.dny123.com/wechat_login_callback"
    try:
        result = requests.get(target_url, params=query_params)
        print("dny123 old callback")
        print(result.text)
        return PlainTextResponse(content=echostr)
    except Exception as e:
        print(f"透传GET请求失败: {str(e)}")
        return "success"  # 如果透传失败，返回默认响应

# 处理微信服务器推送的消息
@router.post("/callback")
async def wechat_callback_message(request: Request, db: Session = Depends(database.get_db)):
    try:
        # 打印所有收到的参数
        print("POST /callback 收到的参数:")
        
        # 打印请求头
        headers = dict(request.headers)
        print(f"请求头: {headers}")
        
        # 只读取一次请求体并保存
        try:
            body = await request.body()
            body_str = body.decode('utf-8')
            print(f"请求体: {body_str}")
        except Exception as e:
            print(f"读取请求体失败: {str(e)}")
            body_str = ""
            body = b""
        
        # 处理微信事件消息
        if "<Event><![CDATA[SCAN]]></Event>" in body_str:
            try:
                # 简单解析XML获取关键信息
                from_user = body_str.split("<FromUserName><![CDATA[")[1].split("]]></FromUserName>")[0]
                event_key = body_str.split("<EventKey><![CDATA[")[1].split("]]></EventKey>")[0]
                
                print(f"扫码事件: 用户={from_user}, 场景值={event_key}")
                
                # 如果是登录场景值
                if event_key.startswith("login_"):
                    scene_str = event_key.replace("login_", "")
                    # 查找对应的二维码记录
                    qrcode = db.query(models.QRCodeTicket).filter(
                        models.QRCodeTicket.scene_str == scene_str,
                        models.QRCodeTicket.expire_time > datetime.now(timezone.utc)
                    ).first()
                    
                    if qrcode:
                        # 更新二维码状态
                        qrcode.is_used = True
                        qrcode.openid = from_user
                        db.commit()
                        
                        # 查找或创建微信用户
                        wechat_user = db.query(models.WechatUser).filter(
                            models.WechatUser.openid == from_user
                        ).first()
                        
                        if not wechat_user:
                            wechat_user = models.WechatUser(openid=from_user)
                            db.add(wechat_user)
                            db.commit()
            except Exception as e:
                print(f"处理扫码事件失败: {str(e)}")
        
        # 透传请求到目标地址
        target_url = "https://www.dny123.com/wechat_login_callback"
        
        # 保留原始请求头中的关键信息
        forward_headers = {
            "Content-Type": headers.get("content-type", "text/xml"),
            "User-Agent": headers.get("user-agent", ""),
        }
        
        # 转发请求体和查询参数
        query_params = dict(request.query_params)
        
        # 使用已读取的body而不是再次从request读取
        response = requests.post(
            target_url, 
            data=body,  # 使用已读取的body
            headers=forward_headers,
            params=query_params,
            timeout=10
        )
        
        print(f"透传响应状态码: {response.status_code}")
        print(f"透传响应内容: {response.text}")  # 只打印前200个字符避免日志过长
        
        # 返回微信服务器期望的响应格式
        return PlainTextResponse(content=response.text)
    except Exception as e:
        error_msg = f"处理微信回调失败: {str(e)}"
        print(error_msg)
        logging.error(error_msg)
        return PlainTextResponse(content="success")  # 如果处理失败，返回默认响应

# 获取所有微信用户信息
@router.get("/wechat/users")
async def get_all_wechat_users(superopenid: str, db: Session = Depends(database.get_db)):
    try:
        # 验证超级用户权限
        super_user = db.query(models.WechatUser).filter(
            models.WechatUser.openid == superopenid,
            models.WechatUser.isvip == 2
        ).first()
        
        if not super_user:
            return {"code": 403, "error": "没有权限执行此操作"}
        
        users = db.query(models.WechatUser).all()
        result = []
        for user in users:
            result.append({
                "id": user.id,
                "openid": user.openid,
                "isvip": user.isvip,
                "todaycount": user.todaycount,
                "subscribe_time": user.subscribe_time
            })
        return {"code": 200, "data": result, "total": len(result)}
    except Exception as e:
        return {"code": 500, "error": f"获取用户列表失败: {str(e)}"}

# 修改微信用户VIP状态和每日使用次数
class UpdateWechatUserParams(BaseModel):
    superopenid: str
    openid: str
    isvip: Optional[int] = None
    todaycount: Optional[int] = None

@router.post("/wechat/user/update")
async def update_wechat_user(params: UpdateWechatUserParams, db: Session = Depends(database.get_db)):
    try:
        # 验证超级用户权限
        super_user = db.query(models.WechatUser).filter(
            models.WechatUser.openid == params.superopenid,
            models.WechatUser.isvip == 2
        ).first()
        
        if not super_user:
            return {"code": 403, "error": "没有权限执行此操作"}
        
        user = db.query(models.WechatUser).filter(models.WechatUser.openid == params.openid).first()
        if not user:
            return {"code": 404, "error": "用户不存在"}
        
        # 更新VIP状态
        if params.isvip is not None:
            user.isvip = params.isvip
        
        # 更新每日使用次数
        if params.todaycount is not None:
            user.todaycount = params.todaycount
        
        db.commit()
        
        return {
            "code": 200, 
            "message": "更新成功", 
            "data": {
                "openid": user.openid,
                "isvip": user.isvip,
                "todaycount": user.todaycount
            }
        }
    except Exception as e:
        return {"code": 500, "error": f"更新用户信息失败: {str(e)}"}


