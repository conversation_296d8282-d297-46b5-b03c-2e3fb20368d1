# 数据库字符集更新为UTF8MB4

## 概述

本文档说明如何将数据库中所有表的字符集从utf8更新为utf8mb4，以支持完整的Unicode字符集，包括emoji表情符号。

## 为什么需要UTF8MB4

### UTF8 vs UTF8MB4
- **UTF8**: MySQL中的utf8实际上是utf8mb3，只支持最多3字节的UTF-8字符
- **UTF8MB4**: 支持完整的4字节UTF-8字符，包括emoji表情符号

### 主要优势
- ✅ 支持emoji表情符号
- ✅ 支持完整的Unicode字符集
- ✅ 更好的国际化支持
- ✅ 未来兼容性更好

## 已完成的修改

### 1. 数据库连接配置
修改了 `app/database/database.py` 中的数据库连接配置：

```python
engine = create_engine(
    f'mysql+pymysql://{username}:{password}@{host}:{port}/{database}',
    echo=True,
    connect_args={
        "charset": "utf8mb4",
        "collation": "utf8mb4_unicode_ci"
    }
)
```

### 2. 数据模型配置
为所有模型类添加了字符集配置：

```python
class User(Base):
    __tablename__ = 'user'
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}
```

### 3. 关联表配置
为多对多关联表添加了字符集配置：

```python
image_tag_association = Table(
    'image_tag_association', 
    Base.metadata,
    Column('image_id', Integer, ForeignKey('images.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True),
    mysql_charset='utf8mb4',
    mysql_collate='utf8mb4_unicode_ci'
)
```

## 数据库迁移

### 方法一：使用Python脚本（推荐）

```bash
# 执行字符集更新脚本
python scripts/update_charset.py
```

**特点**：
- 自动检查当前字符集
- 提供备份提醒
- 详细的执行日志
- 自动验证更新结果

### 方法二：使用SQL脚本

```bash
# 在MySQL客户端中执行
mysql -h rm-wz9kc1ko7s93t1exclo.mysql.rds.aliyuncs.com -u dny123 -p dny123 < scripts/update_charset_to_utf8mb4.sql
```

### 方法三：使用Alembic迁移

```bash
# 如果alembic可用
alembic upgrade head
```

## 执行步骤

### 1. 备份数据库（重要！）
```bash
mysqldump -h rm-wz9kc1ko7s93t1exclo.mysql.rds.aliyuncs.com -u dny123 -p dny123 > backup_before_utf8mb4.sql
```

### 2. 执行字符集更新
```bash
cd code
python scripts/update_charset.py
```

### 3. 验证更新结果
```sql
-- 查看数据库字符集
SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'dny123';

-- 查看所有表的字符集
SELECT TABLE_NAME, TABLE_COLLATION 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'dny123' 
ORDER BY TABLE_NAME;
```

## 影响的表

以下表将被更新为utf8mb4字符集：

1. **用户相关表**
   - `user` - 用户表
   - `role` - 角色表
   - `auth` - 权限表
   - `wechat_users` - 微信用户表
   - `guest_users` - 访客用户表
   - `qrcode_tickets` - 二维码票据表

2. **内容管理表**
   - `site_configs` - 站点配置表
   - `link_groups` - 链接组表

3. **图片管理表**
   - `images` - 图片表
   - `tags` - 标签表
   - `image_tag_association` - 图片标签关联表

4. **文章管理表**
   - `articles` - 文章表
   - `article_authors` - 文章作者表
   - `article_categories` - 文章分类表
   - `article_tags` - 文章标签表
   - `article_tag_association` - 文章标签关联表

## 注意事项

### 1. 数据备份
- ⚠️ **执行前必须备份数据库**
- 建议在非生产环境先测试
- 保留备份文件直到确认更新成功

### 2. 存储空间
- UTF8MB4可能会增加存储空间使用
- 每个字符最多占用4字节（vs UTF8的3字节）

### 3. 索引长度
- 某些长字符串字段的索引可能需要调整
- MySQL对索引长度有限制

### 4. 应用兼容性
- 确保应用程序支持UTF8MB4
- 检查数据库连接配置

## 验证测试

### 1. 插入emoji测试
```sql
-- 测试插入emoji
INSERT INTO wechat_users (openid, nickname) VALUES ('test123', '测试用户😀🎉');

-- 查询验证
SELECT nickname FROM wechat_users WHERE openid = 'test123';
```

### 2. 中文字符测试
```sql
-- 测试中文字符
INSERT INTO articles (title, content, channel, publish_time, author_id, creator_id, updater_id) 
VALUES ('测试标题', '这是一个包含中文和emoji的测试内容😊', '头条', NOW(), 1, 1, 1);
```

## 回滚方案

如果需要回滚到UTF8字符集：

```sql
-- 回滚数据库字符集
ALTER DATABASE dny123 CHARACTER SET = utf8 COLLATE = utf8_general_ci;

-- 回滚表字符集（示例）
ALTER TABLE user CONVERT TO CHARACTER SET utf8 COLLATE utf8_general_ci;
-- ... 其他表类似
```

## 常见问题

### Q: 更新后现有数据会丢失吗？
A: 不会。字符集转换会保留现有数据，只是改变存储格式。

### Q: 更新需要多长时间？
A: 取决于表的大小，通常几分钟到几十分钟不等。

### Q: 可以只更新部分表吗？
A: 可以，但建议全部更新以保持一致性。

### Q: 更新失败怎么办？
A: 使用备份文件恢复，检查错误日志，解决问题后重新执行。

## 总结

UTF8MB4字符集更新是一个重要的数据库优化，能够：
- ✅ 支持完整的Unicode字符集
- ✅ 解决emoji显示问题
- ✅ 提高国际化支持
- ✅ 为未来功能扩展做准备

执行更新前请务必备份数据库，并在测试环境验证无误后再在生产环境执行。
