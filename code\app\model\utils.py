from passlib.context import Crypt<PERSON>ontext
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from datetime import datetime, timedelta
from typing import Optional
from app.model import schemas

class HourliveException(Exception):
    def __init__(self, message, status_code = 400):
        self.message = message
        self.status_code = status_code

# 加密密码用的context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT配置
SECRET_KEY = "hourlive_donghuan_password"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return schemas.Token(access_token=encoded_jwt, token_type="bearer")


# 计算小时数方法
def calculate_hours(start_date, start_time, end_date, end_time):
    #求小时数
    time1 = start_date + " " + start_time
    time2 = end_date + " " + end_time
    datetime_format = "%Y-%m-%d %H:%M"
    dt1 = datetime.strptime(time1, datetime_format)
    dt2 = datetime.strptime(time2, datetime_format)
    time_difference = dt2 - dt1
    hours_difference = time_difference.total_seconds() / 3600
    return hours_difference