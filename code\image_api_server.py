#!/usr/bin/env python3
"""
独立的图片管理API服务器

提供图片管理、标签管理、分类管理的独立API服务
"""

import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import ValidationError
from fastapi.exceptions import RequestValidationError

from app.model import models, schemas
from app.database import database
from app.model.utils import HourliveException

# 导入路由模块
from app.routers.biz_routes import image_api_routers
from app.routers.biz_routes import image_tag_api_routers
from app.routers.biz_routes import image_category_api_routers

def create_app():
    """创建FastAPI应用"""
    app = FastAPI(
        title="图片管理系统API",
        description="""
        ## 图片管理系统API
        
        提供完整的图片管理功能，包括：
        
        ### 🖼️ 图片管理
        - 图片上传、查看、编辑、删除
        - 支持批量操作
        - 图片信息统计
        
        ### 🏷️ 标签管理  
        - 标签的增删改查
        - 标签与图片关联
        - 标签使用统计
        
        ### 📁 分类管理
        - 分类查看和统计
        - 分类重命名和删除
        - 分类下图片管理
        
        ### 认证说明
        所有API都需要用户认证，请确保在请求头中包含有效的认证信息。
        """,
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(image_api_routers.router, prefix="/image", tags=["图片管理"])
    app.include_router(image_tag_api_routers.router, prefix="/image-tag", tags=["图片标签管理"])
    app.include_router(image_category_api_routers.router, prefix="/image-category", tags=["图片分类管理"])
    
    # 异常处理器
    @app.exception_handler(HourliveException)
    async def hourlive_exception_handler(request: Request, exc: HourliveException):
        return JSONResponse(
            status_code=exc.status_code,
            content=schemas.StandardResponse(
                success=False, 
                message=exc.message
            ).dict()
        )

    @app.exception_handler(ValidationError)
    async def validation_exception_handler(request: Request, exc: ValidationError):
        return JSONResponse(
            status_code=400,
            content=schemas.StandardResponse(
                success=False, 
                message=str(exc)
            ).dict()
        )

    @app.exception_handler(RequestValidationError)
    async def request_validation_exception_handler(request: Request, exc: RequestValidationError):
        return JSONResponse(
            status_code=422,
            content=schemas.StandardResponse(
                success=False, 
                message=str(exc)
            ).dict()
        )
    
    # 健康检查接口
    @app.get("/health", tags=["系统"])
    async def health_check():
        """健康检查接口"""
        return schemas.StandardResponse(
            success=True,
            data={"status": "healthy", "service": "image-management-api"},
            message="服务运行正常"
        )
    
    # 根路径重定向到文档
    @app.get("/", tags=["系统"])
    async def root():
        """根路径，重定向到API文档"""
        return {
            "message": "图片管理系统API",
            "docs_url": "/docs",
            "redoc_url": "/redoc",
            "openapi_url": "/openapi.json"
        }
    
    return app

# 创建应用实例
app = create_app()

# 启动时创建数据库表
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    try:
        # 创建数据库表
        models.Base.metadata.create_all(bind=database.engine)
        print("✅ 数据库表初始化完成")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")

if __name__ == "__main__":
    print("🚀 启动图片管理API服务...")
    print("📖 API文档地址: http://localhost:8001/docs")
    print("📚 ReDoc文档地址: http://localhost:8001/redoc")
    
    uvicorn.run(
        "image_api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
