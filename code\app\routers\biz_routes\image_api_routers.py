import logging
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Query, Form
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_

from app.database import database
from app.model import models, schemas
from app.model.dependency import get_current_user
from app.oss import oss

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# 图片基础CRUD接口

@router.post("/upload", response_model=schemas.StandardResponse)
async def upload_image(
    file: UploadFile = File(...),
    original_name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    category: Optional[str] = Form(None),
    is_public: bool = Form(True),
    tag_ids: Optional[str] = Form(None),  # 接收逗号分隔的标签ID字符串
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    上传图片
    """
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图片文件")
        
        # 上传到OSS
        upload_result = oss.upload_image_to_images_directory(file)
        
        if upload_result['status'] != 200:
            raise HTTPException(status_code=500, detail="文件上传失败")
        
        # 解析标签ID
        tag_id_list = []
        if tag_ids:
            try:
                tag_id_list = [int(id.strip()) for id in tag_ids.split(',') if id.strip()]
            except ValueError:
                raise HTTPException(status_code=400, detail="标签ID格式错误")
        
        # 验证标签是否存在
        if tag_id_list:
            existing_tags = db.query(models.Tag).filter(models.Tag.id.in_(tag_id_list)).all()
            if len(existing_tags) != len(tag_id_list):
                raise HTTPException(status_code=400, detail="某些标签不存在")
        
        # 创建图片记录
        db_image = models.Image(
            original_name=original_name or upload_result['original_name'],
            new_name=upload_result['new_name'],
            access_url=upload_result['access_url'],
            file_size=upload_result['file_size'],
            file_type=upload_result['file_type'],
            width=upload_result['width'],
            height=upload_result['height'],
            description=description,
            is_public=is_public,
            category=category,
            uploader_id=current_user.id
        )
        
        db.add(db_image)
        db.flush()  # 获取生成的ID
        
        # 关联标签
        if tag_id_list:
            tags = db.query(models.Tag).filter(models.Tag.id.in_(tag_id_list)).all()
            db_image.tags = tags
        
        db.commit()
        db.refresh(db_image)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ImageRead.from_orm(db_image),
            message="图片上传成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传图片失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.get("/list", response_model=schemas.StandardResponse)
async def get_image_list(
    tag_ids: Optional[str] = Query(None, description="标签ID列表，逗号分隔"),
    category: Optional[str] = Query(None, description="分类筛选"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    is_public: Optional[bool] = Query(None, description="是否公开"),
    uploader_id: Optional[int] = Query(None, description="上传者ID"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取图片列表，支持分页和筛选
    """
    try:
        query = db.query(models.Image).options(joinedload(models.Image.tags))
        
        # 标签筛选
        if tag_ids:
            try:
                tag_id_list = [int(id.strip()) for id in tag_ids.split(',') if id.strip()]
                if tag_id_list:
                    query = query.join(models.Image.tags).filter(models.Tag.id.in_(tag_id_list))
            except ValueError:
                raise HTTPException(status_code=400, detail="标签ID格式错误")
        
        # 分类筛选
        if category:
            query = query.filter(models.Image.category == category)
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    models.Image.original_name.contains(keyword),
                    models.Image.description.contains(keyword)
                )
            )
        
        # 公开性筛选
        if is_public is not None:
            query = query.filter(models.Image.is_public == is_public)
        
        # 上传者筛选
        if uploader_id:
            query = query.filter(models.Image.uploader_id == uploader_id)
        
        # 按创建时间降序排列
        query = query.order_by(models.Image.create_time.desc())
        
        # 总记录数
        total_records = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        images = query.offset(offset).limit(page_size).all()
        
        return schemas.StandardResponse(
            success=True,
            data=[schemas.ImageRead.from_orm(image) for image in images],
            total_records=total_records,
            message="获取图片列表成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")

@router.get("/{image_id}", response_model=schemas.StandardResponse)
async def get_image_detail(
    image_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取图片详情
    """
    image = db.query(models.Image).options(joinedload(models.Image.tags)).filter(
        models.Image.id == image_id
    ).first()
    
    if not image:
        raise HTTPException(status_code=404, detail="图片不存在")
    
    return schemas.StandardResponse(
        success=True,
        data=schemas.ImageRead.from_orm(image),
        message="获取图片详情成功"
    )

@router.put("/{image_id}", response_model=schemas.StandardResponse)
async def update_image(
    image_id: int,
    image_update: schemas.ImageUpdate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    更新图片信息
    """
    try:
        image = db.query(models.Image).filter(models.Image.id == image_id).first()
        
        if not image:
            raise HTTPException(status_code=404, detail="图片不存在")
        
        # 更新基本信息
        update_data = image_update.dict(exclude={'tag_ids'}, exclude_unset=True)
        for field, value in update_data.items():
            setattr(image, field, value)
        
        # 更新标签关联
        if image_update.tag_ids is not None:
            if image_update.tag_ids:
                # 验证标签是否存在
                tags = db.query(models.Tag).filter(models.Tag.id.in_(image_update.tag_ids)).all()
                if len(tags) != len(image_update.tag_ids):
                    raise HTTPException(status_code=400, detail="某些标签不存在")
                image.tags = tags
            else:
                image.tags = []
        
        image.update_time = datetime.now()
        db.commit()
        db.refresh(image)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ImageRead.from_orm(image),
            message="图片信息更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新图片失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@router.delete("/{image_id}", response_model=schemas.StandardResponse)
async def delete_image(
    image_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    删除图片
    """
    try:
        image = db.query(models.Image).filter(models.Image.id == image_id).first()
        
        if not image:
            raise HTTPException(status_code=404, detail="图片不存在")
        
        # 从OSS删除文件
        try:
            oss.delete_image_from_images_directory(image.new_name)
        except Exception as e:
            logger.warning(f"从OSS删除文件失败: {str(e)}")
        
        # 从数据库删除记录（级联删除标签关联）
        db.delete(image)
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            message="图片删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除图片失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.post("/batch-delete", response_model=schemas.StandardResponse)
async def batch_delete_images(
    image_ids: List[int],
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    批量删除图片
    """
    try:
        images = db.query(models.Image).filter(models.Image.id.in_(image_ids)).all()
        
        if not images:
            raise HTTPException(status_code=404, detail="没有找到要删除的图片")
        
        deleted_count = 0
        for image in images:
            try:
                # 从OSS删除文件
                oss.delete_image_from_images_directory(image.new_name)
            except Exception as e:
                logger.warning(f"从OSS删除文件失败: {str(e)}")
            
            # 从数据库删除
            db.delete(image)
            deleted_count += 1
        
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            data={"deleted_count": deleted_count},
            message=f"成功删除 {deleted_count} 张图片"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除图片失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")

