from fastapi import FastAP<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from datetime import timedelta
from app.model import schemas,models
from app.model.utils import get_password_hash, verify_password, create_access_token
from app.model.dependency import get_current_user
from fastapi import APIRouter
from sqlalchemy.orm import Session
from app.database import database
from sqlalchemy.orm import joinedload
from pydantic import BaseModel, Field, validator
from pydantic import BaseModel
from typing import Optional, Any
from app.model.utils import HourliveException
from fastapi import Body
from fastapi import Form

router = APIRouter()
ACCESS_TOKEN_EXPIRE_MINUTES = 1440

#注册为机构或者用户
class UserCreate(BaseModel):
    account: str = Field(..., max_length=50)
    password: str = Field(..., max_length=50)
    user_type: int = Field(default=0) # 0普通用户

    name: Optional[str] = Field(None, max_length=100)
    avatar: Optional[str] = Field(None, max_length=200)
    mobile: Optional[str] = Field(None, max_length=50)
    email: Optional[str] = Field(None, max_length=100)

class UserLogin(BaseModel):
    username: str = Field(..., max_length=50)
    password: str = Field(..., max_length=50)



@router.post("/register", response_model=schemas.StandardResponse)
async def register(user: UserCreate, db: Session = Depends(database.get_db)):
    """
    用户使用account、password注册，并选择是机构还是顾客
    """
    #检查用户是否存在
    existing_user = db.query(models.User).filter(models.User.account == user.account).first()
    if existing_user:
        raise HourliveException(message="accountexist")

    try:
        #创建hash密码
        hashed_password = get_password_hash(user.password)
        
        #创建用户
        db_user = models.User()
        db_user.hashed_password = hashed_password
        db_user.account = user.account
        db_user.user_type = user.user_type
        db_user.name = user.name
        db_user.avatar = user.avatar
        db_user.mobile = user.mobile
        db_user.email = user.email
        db_user.role_id = 1

        db.add(db_user)
        db.flush() #获取id


        db.commit()

        #创建token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.account}, expires_delta=access_token_expires
        )
        return schemas.StandardResponse(success=True, token=access_token)

    except Exception as e:
        db.rollback()
        raise HourliveException(message="registerfail")


@router.post("/login", response_model=schemas.StandardResponse)
async def login_for_access_token(user: UserLogin,db: Session = Depends(database.get_db)):
    """
    登录获取token
    """
    existing_user = db.query(models.User).filter(models.User.account == user.username).first()
    if not existing_user:
        raise HourliveException(message="accountnoexist")
    
    if not verify_password(user.password, existing_user.hashed_password):
        raise HourliveException(message="passworderror")
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": existing_user.account}, expires_delta=access_token_expires
    )
    return schemas.StandardResponse(success=True, token=access_token)

@router.get("/me")
async def read_users_me(current_user: models.User = Depends(get_current_user)):
    """
    获取当前用户信息
    """
    print(current_user)
    print(current_user.agency)
    current_user.hashed_password = None
    return current_user

@router.get("/userInfo")
async def read_users_me(current_user: models.User = Depends(get_current_user)):
    """
    获取当前用户信息
    """
    current_user.hashed_password = None
    return schemas.StandardResponse(success=True, data=current_user)


@router.post("/get_hash", response_model=schemas.StandardResponse)
async def get_string_hash(password: str = Form(...)):
    """
    获取字符串的哈希值
    """
    try:
        hashed_value = get_password_hash(password)
        return schemas.StandardResponse(success=True, data={"hashed_value": hashed_value})
    except Exception as e:
        raise HourliveException(message="hashfail")


