from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Bearer
from jose import JW<PERSON>rror, jwt
from pydantic import BaseModel
from typing import Optional

from app.common.constants import UserType
from app.model.utils import SECRET_KEY, ALGORITHM
from app.model import models,schemas
from sqlalchemy.orm import Session
from app.database import database
from sqlalchemy.orm import joinedload

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

class TokenData(BaseModel):
    username: Optional[str] = None

def get_user(db, username: str):
    if username in db:
        user_dict = db[username]
        return models.User(**user_dict)
    return None

def get_current_user(token: str = Depends(oauth2_scheme),db: Session = Depends(database.get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    
    #加载用户基础信息
    user = db.query(models.User).filter(models.User.account == token_data.username).first()
    if user is None:
        raise credentials_exception

    if user.user_type == UserType.COMMON:
        # 普通用户，什么都不加载
        user = db.query(models.User).options(joinedload(models.User.role).joinedload(models.Role.auths)).filter(models.User.id == user.id).first()

    return user
