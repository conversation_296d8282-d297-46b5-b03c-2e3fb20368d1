#!/usr/bin/env python3
"""
图片管理API文档生成器

生成独立的API文档，包括：
1. 图片管理API
2. 图片标签管理API  
3. 图片分类管理API
"""

import json
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

# 导入路由模块
from app.routers.biz_routes import image_api_routers
from app.routers.biz_routes import image_tag_api_routers
from app.routers.biz_routes import image_category_api_routers

def create_image_management_app():
    """创建图片管理相关的FastAPI应用"""
    app = FastAPI(
        title="图片管理系统API",
        description="""
        ## 图片管理系统API文档
        
        本系统提供完整的图片管理功能，包括：
        
        ### 🖼️ 图片管理
        - 图片上传、下载、删除
        - 图片信息查看和编辑
        - 批量操作支持
        
        ### 🏷️ 标签管理
        - 创建、编辑、删除标签
        - 标签与图片的关联管理
        - 标签使用统计
        
        ### 📁 分类管理
        - 图片分类查看和统计
        - 分类重命名和删除
        - 分类下图片管理
        
        ### 🔐 认证说明
        所有API都需要用户认证，请在请求头中包含有效的认证信息。
        
        ### 📝 响应格式
        所有API响应都遵循统一的格式：
        ```json
        {
            "success": true,
            "data": {},
            "message": "操作成功",
            "total_records": 0
        }
        ```
        """,
        version="1.0.0",
        contact={
            "name": "API支持",
            "email": "<EMAIL>"
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT"
        }
    )
    
    # 注册路由
    app.include_router(image_api_routers.router, prefix="/image", tags=["图片管理"])
    app.include_router(image_tag_api_routers.router, prefix="/image-tag", tags=["图片标签管理"])
    app.include_router(image_category_api_routers.router, prefix="/image-category", tags=["图片分类管理"])
    
    return app

def generate_openapi_spec():
    """生成OpenAPI规范文档"""
    app = create_image_management_app()
    
    # 自定义OpenAPI配置
    openapi_schema = get_openapi(
        title="图片管理系统API",
        version="1.0.0",
        description=app.description,
        routes=app.routes,
    )
    
    # 添加服务器信息
    openapi_schema["servers"] = [
        {
            "url": "http://localhost:9000",
            "description": "开发环境"
        },
        {
            "url": "https://api.example.com",
            "description": "生产环境"
        }
    ]
    
    # 添加安全定义
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT认证，请在Authorization头中提供Bearer token"
        }
    }
    
    # 为所有路径添加安全要求
    for path in openapi_schema["paths"]:
        for method in openapi_schema["paths"][path]:
            if method != "options":
                openapi_schema["paths"][path][method]["security"] = [{"BearerAuth": []}]
    
    return openapi_schema

def save_docs():
    """保存API文档到文件"""
    openapi_spec = generate_openapi_spec()
    
    # 保存JSON格式
    with open("docs/image_management_api.json", "w", encoding="utf-8") as f:
        json.dump(openapi_spec, f, ensure_ascii=False, indent=2)
    
    # 生成Markdown文档
    markdown_doc = generate_markdown_doc(openapi_spec)
    with open("docs/image_management_api.md", "w", encoding="utf-8") as f:
        f.write(markdown_doc)
    
    print("✅ API文档已生成:")
    print("📄 JSON格式: docs/image_management_api.json")
    print("📝 Markdown格式: docs/image_management_api.md")

def generate_markdown_doc(openapi_spec):
    """生成Markdown格式的API文档"""
    md = f"""# {openapi_spec['info']['title']}

版本: {openapi_spec['info']['version']}

{openapi_spec['info']['description']}

## 服务器地址

"""
    
    for server in openapi_spec.get('servers', []):
        md += f"- **{server['description']}**: `{server['url']}`\n"
    
    md += "\n## API接口\n\n"
    
    # 按标签分组
    tags = {}
    for path, methods in openapi_spec['paths'].items():
        for method, details in methods.items():
            if method == 'options':
                continue
            tag = details.get('tags', ['其他'])[0]
            if tag not in tags:
                tags[tag] = []
            tags[tag].append({
                'path': path,
                'method': method.upper(),
                'summary': details.get('summary', ''),
                'description': details.get('description', ''),
                'parameters': details.get('parameters', []),
                'responses': details.get('responses', {})
            })
    
    for tag, endpoints in tags.items():
        md += f"### {tag}\n\n"
        for endpoint in endpoints:
            md += f"#### {endpoint['method']} {endpoint['path']}\n\n"
            md += f"**{endpoint['summary']}**\n\n"
            if endpoint['description']:
                md += f"{endpoint['description']}\n\n"
            
            # 参数
            if endpoint['parameters']:
                md += "**参数:**\n\n"
                for param in endpoint['parameters']:
                    required = " (必填)" if param.get('required') else " (可选)"
                    md += f"- `{param['name']}`{required}: {param.get('description', '')}\n"
                md += "\n"
            
            # 响应
            md += "**响应:**\n\n"
            for status, response in endpoint['responses'].items():
                md += f"- `{status}`: {response.get('description', '')}\n"
            md += "\n---\n\n"
    
    return md

if __name__ == "__main__":
    import os
    
    # 创建docs目录
    os.makedirs("docs", exist_ok=True)
    
    # 生成文档
    save_docs()
