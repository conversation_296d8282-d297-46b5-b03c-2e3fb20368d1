"""Create_SiteConfig_table

Revision ID: 33c641859593
Revises: 
Create Date: 2025-06-25 19:33:53.837268

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '33c641859593'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('site_configs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.Column('title', sa.String(length=255), nullable=False, comment='网站标题'),
    sa.Column('link_url', sa.String(length=255), nullable=False, comment='标题链接URL'),
    sa.Column('prompt_text', sa.String(length=500), nullable=False, comment='提示内容文本'),
    sa.Column('logo', sa.String(length=255), nullable=False, comment='logo图片路径'),
    sa.Column('is_enabled', sa.Boolean(), nullable=False, comment='是否启用，True启用，False禁用'),
    sa.Column('is_fixed_position', sa.Boolean(), nullable=True, comment='是否固定位置，True是，False否'),
    sa.Column('is_new_page', sa.Boolean(), nullable=True, comment='是否跳转新页面，True是，False否'),
    sa.Column('publish_time', sa.DateTime(), nullable=False, comment='上架时间'),
    sa.Column('offline_time', sa.DateTime(), nullable=False, comment='下架时间'),
    sa.Column('partner_code', sa.String(length=100), nullable=False, comment='合作伙伴代码'),
    sa.Column('is_hover_enabled', sa.Boolean(), nullable=True, comment='hover是否启用，True启用，False禁用'),
    sa.Column('creator_id', sa.Integer(), nullable=False),
    sa.Column('updater_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['creator_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['updater_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_site_configs_id'), 'site_configs', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_site_configs_id'), table_name='site_configs')
    op.drop_table('site_configs')
    # ### end Alembic commands ###
