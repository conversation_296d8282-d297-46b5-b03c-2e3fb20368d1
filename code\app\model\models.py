from sqlalchemy import Table, Column, Integer, String, ForeignKey, DateTime, Boolean, Text, Float
from sqlalchemy.orm import relationship, backref
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime,timezone
from typing import List
from sqlalchemy.sql import func

Base = declarative_base()

#customer也是品牌方，和时间段会有一个关联关系
# customer_timeslot = Table('customer_timeslot', Base.metadata,
#     Column('customer_id', Integer, ForeignKey('customer.id')),
#     Column('timeslot_id', Integer, ForeignKey('timeslot.id'))
# )


"""
用户为平台用户,可绑定为一种角色,并且可以绑定customer顾客主体、agency机构主体。
"""
class User(Base):
    __tablename__ = 'user'
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    account = Column(String(50), unique=True, index=True)
    hashed_password = Column(String(256))
    user_type = Column(Integer, default=0) # 0普通用户 1机构主体 2顾客主体 8管理员 3主播streamer

    #外键
    role_id = Column(Integer, ForeignKey('role.id'), nullable=True)

    #用户信息
    name = Column(String(100), nullable=True)
    avatar = Column(String(200), nullable=True)
    mobile = Column(String(50), nullable=True)
    email = Column(String(100), nullable=True)
    

"""
角色,拥有多个权限
"""
class Role(Base):
    __tablename__ = 'role'
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    name = Column(String(50), unique=True)
    
    users = relationship("User", backref = backref('role')) #一对多
    auths = relationship("Auth", backref = backref('role')) #一对多

class Auth(Base):
    __tablename__ = 'auth'
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    name = Column(String(50), unique=True)
    code = Column(String(50))
    
    #外键
    role_id = Column(Integer, ForeignKey('role.id'))

class WechatUser(Base):
    __tablename__ = "wechat_users"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    openid = Column(String(50), unique=True, index=True)
    unionid = Column(String(50), nullable=True)
    nickname = Column(String(100), nullable=True)
    headimgurl = Column(String(255), nullable=True)
    subscribe = Column(Boolean, default=False)
    subscribe_time = Column(DateTime, nullable=True)

    isvip = Column(Integer, default = 0)
    todaycount = Column(Integer, default = 30)
    
class QRCodeTicket(Base):
    __tablename__ = "qrcode_tickets"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    scene_str = Column(String(64), unique=True, index=True)
    ticket = Column(String(255))
    url = Column(String(255))
    expire_time = Column(DateTime)
    is_used = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now(timezone.utc))
    openid = Column(String(50))

class GuestUser(Base):
    __tablename__ = "guest_users"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    ip_address = Column(String(50), unique=True, index=True)
    todaycount = Column(Integer, default=5)
    subscribe_time = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now(timezone.utc))

class LinkGroup(Base):
    """连接组模型"""
    __tablename__ = "link_groups"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # 连接组基本信息
    name = Column(String(100), nullable=False, comment="连接名称")
    url = Column(String(500), nullable=False, comment="连接地址")
    
    # 外键关联到SiteConfig
    site_config_id = Column(Integer, ForeignKey('site_configs.id'), nullable=False)
    
    # 操作人员关联
    creator_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    updater_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # 关系定义
    site_config = relationship("SiteConfig", back_populates="link_groups")
    creator = relationship("User", foreign_keys=[creator_id], backref=backref("created_link_groups", lazy="dynamic"))
    updater = relationship("User", foreign_keys=[updater_id], backref=backref("updated_link_groups", lazy="dynamic"))

class SiteConfig(Base):
    __tablename__ = "site_configs"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # 表单字段
    title = Column(String(255), nullable=False, comment="网站标题")
    link_url = Column(String(255), nullable=False, comment="标题链接URL")
    prompt_text = Column(String(500), nullable=False, comment="提示内容文本")
    logo = Column(String(255), nullable=True, comment="logo图片路径")
    is_enabled = Column(Boolean, default=True, nullable=False, comment="是否启用，True启用，False禁用")
    is_fixed_position = Column(Boolean, default=False, comment="是否固定位置，True是，False否")
    is_new_page = Column(Boolean, default=False, comment="是否跳转新页面，True是，False否")
    publish_time = Column(DateTime, nullable=True, comment="上架时间")
    offline_time = Column(DateTime, nullable=True, comment="下架时间")
    partner_code = Column(String(100), nullable=False, comment="合作伙伴代码")
    is_hover_enabled = Column(Boolean, default=False, comment="hover是否启用，True启用，False禁用")
    
    # 操作人员关联
    creator_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    updater_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # 关系定义
    creator = relationship("User", foreign_keys=[creator_id], backref=backref("created_site_configs", lazy="dynamic"))
    updater = relationship("User", foreign_keys=[updater_id], backref=backref("updated_site_configs", lazy="dynamic"))
    
    # 连接组关系定义
    link_groups = relationship("LinkGroup", back_populates="site_config", cascade="all, delete-orphan")

# 图片和标签的多对多关联表
image_tag_association = Table(
    'image_tag_association',
    Base.metadata,
    Column('image_id', Integer, ForeignKey('images.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True),
    mysql_charset='utf8mb4',
    mysql_collate='utf8mb4_unicode_ci'
)

class Tag(Base):
    """标签表"""
    __tablename__ = "tags"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    name = Column(String(50), nullable=False, unique=True, index=True, comment="标签名称")
    description = Column(String(255), nullable=True, comment="标签描述")
    
    # 操作人员关联
    creator_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # 关系定义
    creator = relationship("User", backref=backref("created_tags", lazy="dynamic"))
    images = relationship("Image", secondary=image_tag_association, back_populates="tags")

class Image(Base):
    """图片资源表"""
    __tablename__ = "images"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # 图片基本信息
    original_name = Column(String(255), nullable=False, comment="图片原始名称")
    new_name = Column(String(255), nullable=False, unique=True, index=True, comment="图片新名称（存储名）")
    access_url = Column(String(500), nullable=False, comment="图片访问URL")
    file_size = Column(Integer, nullable=True, comment="文件大小（字节）")
    file_type = Column(String(50), nullable=True, comment="文件类型（如jpg, png等）")
    width = Column(Integer, nullable=True, comment="图片宽度")
    height = Column(Integer, nullable=True, comment="图片高度")
    
    # 业务字段
    description = Column(Text, nullable=True, comment="图片描述")
    is_public = Column(Boolean, default=True, comment="是否公开访问")
    category = Column(String(50), nullable=True, comment="图片分类")
    
    # 操作人员关联
    uploader_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # 关系定义
    uploader = relationship("User", backref=backref("uploaded_images", lazy="dynamic"))
    tags = relationship("Tag", secondary=image_tag_association, back_populates="images")

# 文章标签关联表
article_tag_association = Table(
    'article_tag_association',
    Base.metadata,
    Column('article_id', Integer, ForeignKey('articles.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('article_tags.id'), primary_key=True),
    mysql_charset='utf8mb4',
    mysql_collate='utf8mb4_unicode_ci'
)

class ArticleCategory(Base):
    """标签分类表"""
    __tablename__ = "article_categories"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    name = Column(String(20), nullable=False, comment="分类名称")
    weight = Column(String(1), nullable=False, comment="分类权重：1.合作伙伴 2.文章性质 3.服务品类 4.国家/地区 5.平台/独立站")
    seo_title = Column(String(100), nullable=True, comment="SEO标题")
    seo_keywords = Column(String(100), nullable=True, comment="SEO关键字")
    seo_description = Column(String(300), nullable=True, comment="SEO描述")
    
    # 操作人员关联
    creator_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    updater_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # 关系定义
    creator = relationship("User", foreign_keys=[creator_id])
    updater = relationship("User", foreign_keys=[updater_id])
    tags = relationship("ArticleTag", back_populates="category")

class ArticleAuthor(Base):
    """文章作者表"""
    __tablename__ = "article_authors"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    wechat_nickname = Column(String(30), nullable=False, comment="微信昵称")
    author_name = Column(String(30), nullable=False, comment="作者名")
    
    # 操作人员关联
    creator_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    updater_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # 关系定义
    creator = relationship("User", foreign_keys=[creator_id])
    updater = relationship("User", foreign_keys=[updater_id])
    articles = relationship("Article", back_populates="author")

class ArticleTag(Base):
    """文章标签表"""
    __tablename__ = "article_tags"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    name = Column(String(20), nullable=False, unique=True, index=True, comment="标签名")
    first_letter = Column(String(1), nullable=False, comment="首字母")
    article_count = Column(Integer, default=0, comment="关联文章数")
    article_read_count = Column(Integer, default=0, comment="关联文章阅读数")
    
    # 外键关联
    category_id = Column(Integer, ForeignKey('article_categories.id'), nullable=False)
    
    # 操作人员关联
    creator_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    updater_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # 关系定义
    category = relationship("ArticleCategory", back_populates="tags")
    creator = relationship("User", foreign_keys=[creator_id])
    updater = relationship("User", foreign_keys=[updater_id])
    articles = relationship("Article", secondary=article_tag_association, back_populates="tags")

class Article(Base):
    """文章表"""
    __tablename__ = "articles"
    __table_args__ = {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci'}

    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    title = Column(String(100), nullable=False, comment="标题")
    style = Column(String(20), nullable=False, comment="风格：富文本、markdown")
    content = Column(Text, nullable=False, comment="文章内容")
    channel = Column(String(20), nullable=False, comment="发布频道：头条、百科、快讯")
    cover_image = Column(String(500), nullable=True, comment="文章封面")
    summary = Column(String(300), nullable=True, comment="简介")
    share_image = Column(String(500), nullable=True, comment="分享封面")
    publish_time = Column(DateTime, nullable=False, comment="发表时间")
    is_visible = Column(Boolean, default=True, comment="是否展示")
    seo_title = Column(String(100), nullable=True, comment="SEO标题")
    seo_keywords = Column(String(100), nullable=True, comment="SEO关键字")
    seo_description = Column(String(300), nullable=True, comment="SEO描述")
    view_count = Column(Integer, default=0, comment="浏览数")
    like_count = Column(Integer, default=0, comment="点赞数")
    favorite_count = Column(Integer, default=0, comment="收藏数")
    comment_count = Column(Integer, default=0, comment="评论数")
    status = Column(String(1), nullable=False, default="0", comment="状态：0.草稿 1.待审核 2.审核中 3.已发布")
    
    # 外键关联
    author_id = Column(Integer, ForeignKey('article_authors.id'), nullable=False)
    
    # 操作人员关联
    creator_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    updater_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # 关系定义
    author = relationship("ArticleAuthor", back_populates="articles")
    creator = relationship("User", foreign_keys=[creator_id])
    updater = relationship("User", foreign_keys=[updater_id])
    tags = relationship("ArticleTag", secondary=article_tag_association, back_populates="articles")
