import logging
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.database import database
from app.model import models, schemas
from app.model.dependency import get_current_user

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/create", response_model=schemas.StandardResponse)
async def create_tag(
    tag: schemas.TagCreate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    创建图片标签
    
    - **name**: 标签名称（必填，唯一）
    - **description**: 标签描述（可选）
    - **color**: 标签颜色（可选，用于前端显示）
    """
    try:
        # 检查标签名是否已存在
        existing_tag = db.query(models.Tag).filter(models.Tag.name == tag.name).first()
        if existing_tag:
            raise HTTPException(status_code=400, detail="标签名已存在")
        
        db_tag = models.Tag(**tag.dict(), creator_id=current_user.id)
        db.add(db_tag)
        db.commit()
        db.refresh(db_tag)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.TagRead.from_orm(db_tag),
            message="标签创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建标签失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")

@router.get("/list", response_model=schemas.StandardResponse)
async def get_tag_list(
    keyword: Optional[str] = Query(None, description="关键词搜索（标签名或描述）"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=100, description="每页大小"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取图片标签列表
    
    支持分页和关键词搜索功能
    """
    try:
        query = db.query(models.Tag)
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    models.Tag.name.contains(keyword),
                    models.Tag.description.contains(keyword)
                )
            )
        
        # 按名称排序
        query = query.order_by(models.Tag.name)
        
        # 总记录数
        total_records = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        tags = query.offset(offset).limit(page_size).all()
        
        return schemas.StandardResponse(
            success=True,
            data=[schemas.TagRead.from_orm(tag) for tag in tags],
            total_records=total_records,
            message="获取标签列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取标签列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")

@router.get("/detail/{tag_id}", response_model=schemas.StandardResponse)
async def get_tag_detail(
    tag_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取标签详情
    
    包含标签的基本信息和使用统计
    """
    try:
        tag = db.query(models.Tag).filter(models.Tag.id == tag_id).first()
        
        if not tag:
            raise HTTPException(status_code=404, detail="标签不存在")
        
        # 统计使用此标签的图片数量
        image_count = db.query(models.Image).join(models.Image.tags).filter(
            models.Tag.id == tag_id
        ).count()
        
        tag_data = schemas.TagRead.from_orm(tag).dict()
        tag_data['image_count'] = image_count
        
        return schemas.StandardResponse(
            success=True,
            data=tag_data,
            message="获取标签详情成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取标签详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取详情失败: {str(e)}")

@router.put("/update/{tag_id}", response_model=schemas.StandardResponse)
async def update_tag(
    tag_id: int,
    tag_update: schemas.TagUpdate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    更新标签信息
    
    可以更新标签的名称、描述和颜色
    """
    try:
        tag = db.query(models.Tag).filter(models.Tag.id == tag_id).first()
        
        if not tag:
            raise HTTPException(status_code=404, detail="标签不存在")
        
        # 检查标签名是否已存在
        if tag_update.name and tag_update.name != tag.name:
            existing_tag = db.query(models.Tag).filter(models.Tag.name == tag_update.name).first()
            if existing_tag:
                raise HTTPException(status_code=400, detail="标签名已存在")
        
        # 更新标签信息
        update_data = tag_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(tag, field, value)
        
        tag.update_time = datetime.now()
        db.commit()
        db.refresh(tag)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.TagRead.from_orm(tag),
            message="标签更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新标签失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@router.delete("/delete/{tag_id}", response_model=schemas.StandardResponse)
async def delete_tag(
    tag_id: int,
    force: bool = Query(False, description="是否强制删除（即使有图片在使用）"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    删除标签
    
    - **force**: 如果为true，即使有图片在使用此标签也会删除
    - 默认情况下，如果有图片在使用此标签，删除会失败
    """
    try:
        tag = db.query(models.Tag).filter(models.Tag.id == tag_id).first()
        
        if not tag:
            raise HTTPException(status_code=404, detail="标签不存在")
        
        # 检查是否有图片使用此标签
        image_count = db.query(models.Image).join(models.Image.tags).filter(
            models.Tag.id == tag_id
        ).count()
        
        if image_count > 0 and not force:
            raise HTTPException(
                status_code=400, 
                detail=f"无法删除标签，还有 {image_count} 张图片正在使用此标签。如需强制删除，请设置 force=true"
            )
        
        db.delete(tag)
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            message=f"标签删除成功{f'（已从 {image_count} 张图片中移除）' if image_count > 0 else ''}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除标签失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/images/{tag_id}", response_model=schemas.StandardResponse)
async def get_tag_images(
    tag_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取使用指定标签的图片列表
    """
    try:
        # 验证标签是否存在
        tag = db.query(models.Tag).filter(models.Tag.id == tag_id).first()
        if not tag:
            raise HTTPException(status_code=404, detail="标签不存在")
        
        # 查询使用此标签的图片
        query = db.query(models.Image).join(models.Image.tags).filter(
            models.Tag.id == tag_id
        ).order_by(models.Image.create_time.desc())
        
        # 总记录数
        total_records = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        images = query.offset(offset).limit(page_size).all()
        
        return schemas.StandardResponse(
            success=True,
            data=[schemas.ImageRead.from_orm(image) for image in images],
            total_records=total_records,
            message=f"获取标签 '{tag.name}' 的图片列表成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取标签图片列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")
