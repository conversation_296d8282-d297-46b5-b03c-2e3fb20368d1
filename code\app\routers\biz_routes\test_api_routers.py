from fastapi import FastAP<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from datetime import timedelta
from app.model import schemas,models
from app.model.utils import get_password_hash, verify_password, create_access_token
from app.model.dependency import get_current_user
from fastapi import APIRouter
from sqlalchemy.orm import Session
from app.database import database
from sqlalchemy.orm import joinedload
from pydantic import BaseModel, Field, validator
from pydantic import BaseModel
from typing import Optional, Any
from app.model.utils import HourliveException
from fastapi import Body
from fastapi import Form
import base64
import json
import hashlib
import httpx
import uuid
import random
import string
import aiohttp
from io import BytesIO

router = APIRouter()

@router.get("/query", response_model=schemas.StandardResponse)
async def get_entries(db: Session = Depends(database.get_db)):
    # 随机生成5-10个入口数据
    num_entries = random.randint(5, 10)
    entries = []
    
    for i in range(num_entries):
        # 随机生成名称和URL
        random_name = f"入口{i+1}"
        random_url = f"https://example.com/path{i+1}"
        
        entries.append({
            "name": random_name,
            "url": random_url
        })
    
    return schemas.StandardResponse(success=True, data=entries)
    