# Vue.js 热门文章接口集成指南

## 接口概述

热门文章接口提供日榜、周榜、月榜三种类型的热门文章查询，按阅读量降序排列，最多返回前6篇文章。

## 接口信息

**接口地址**: `GET /public-article/hot`

**请求参数**:
- `period` (必填): 榜单类型
  - `daily`: 日榜（最近1天）
  - `weekly`: 周榜（最近1周）
  - `monthly`: 月榜（最近1月）
- `limit` (可选): 返回文章数量，默认6篇，最多20篇

## Vue.js 集成示例

### 1. API 服务封装

```javascript
// api/articleService.js
import axios from 'axios'

const API_BASE_URL = 'http://localhost:9000'

export const articleService = {
  // 获取热门文章
  async getHotArticles(period = 'weekly', limit = 6) {
    try {
      const response = await axios.get(`${API_BASE_URL}/public-article/hot`, {
        params: { period, limit }
      })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || '获取热门文章失败')
    }
  },

  // 获取文章详情
  async getArticleDetail(articleId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/public-article/detail/${articleId}`)
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || '获取文章详情失败')
    }
  }
}
```

### 2. Vue 组件示例

```vue
<template>
  <div class="hot-articles">
    <!-- 榜单切换 -->
    <div class="tab-container">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { active: activeTab === tab.value }]"
        @click="switchTab(tab.value)"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      加载中...
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      {{ error }}
      <button @click="loadHotArticles" class="retry-btn">重试</button>
    </div>

    <!-- 文章列表 -->
    <div v-else class="articles-list">
      <div class="period-info">
        <h3>{{ getCurrentPeriodName() }}</h3>
      </div>

      <div
        v-for="(article, index) in hotData"
        :key="article.id"
        class="article-item"
        @click="goToDetail(article.id)"
      >
        <div class="rank-number">{{ index + 1 }}</div>
        <div class="article-cover">
          <img 
            :src="article.cover_image || '/default-cover.jpg'" 
            :alt="article.title"
            @error="handleImageError"
          />
        </div>
        <div class="article-info">
          <h4 class="article-title">{{ article.title }}</h4>
          <div class="article-meta">
            <span class="author">{{ article.author.author_name }}</span>
            <span class="publish-time">{{ formatDate(article.publish_time) }}</span>
            <span class="view-count">{{ formatViewCount(article.view_count) }}阅读</span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!hotData?.length" class="empty-state">
        暂无热门文章
      </div>
    </div>
  </div>
</template>

<script>
import { articleService } from '@/api/articleService'

export default {
  name: 'HotArticles',
  data() {
    return {
      activeTab: 'weekly',
      loading: false,
      error: null,
      hotData: null,
      tabs: [
        { value: 'daily', label: '日榜' },
        { value: 'weekly', label: '周榜' },
        { value: 'monthly', label: '月榜' }
      ]
    }
  },
  mounted() {
    this.loadHotArticles()
  },
  methods: {
    // 切换榜单
    async switchTab(period) {
      if (this.activeTab === period) return
      this.activeTab = period
      await this.loadHotArticles()
    },

    // 加载热门文章
    async loadHotArticles() {
      this.loading = true
      this.error = null
      
      try {
        const result = await articleService.getHotArticles(this.activeTab, 6)
        if (result.success) {
          this.hotData = result.data  // 直接是文章数组
        } else {
          this.error = result.message || '获取数据失败'
        }
      } catch (error) {
        this.error = error.message
      } finally {
        this.loading = false
      }
    },

    // 跳转到文章详情
    goToDetail(articleId) {
      this.$router.push(`/article/${articleId}`)
    },

    // 获取当前榜单名称
    getCurrentPeriodName() {
      const tab = this.tabs.find(t => t.value === this.activeTab)
      return tab ? tab.label : '热门文章'
    },

    // 格式化日期
    formatDate(dateString) {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString()
    },

    // 格式化阅读量
    formatViewCount(count) {
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w'
      }
      if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k'
      }
      return count.toString()
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/default-cover.jpg'
    }
  }
}
</script>

<style scoped>
.hot-articles {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.tab-container {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.tab-button {
  padding: 10px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-button.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.tab-button:hover {
  color: #1890ff;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  color: #666;
}

.retry-btn {
  margin-left: 10px;
  padding: 5px 10px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.period-info {
  margin-bottom: 20px;
}

.period-info h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.time-range {
  margin: 0;
  color: #999;
  font-size: 14px;
}

.article-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.article-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.rank-number {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-right: 15px;
  min-width: 30px;
}

.article-cover {
  width: 80px;
  height: 60px;
  margin-right: 15px;
  overflow: hidden;
  border-radius: 4px;
}

.article-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-info {
  flex: 1;
}

.article-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hot-articles {
    padding: 10px;
  }
  
  .article-item {
    padding: 10px;
  }
  
  .article-cover {
    width: 60px;
    height: 45px;
  }
  
  .article-title {
    font-size: 14px;
  }
  
  .article-meta {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
```

## 接口返回数据结构

```javascript
// 成功响应示例
{
  "success": true,
  "data": [
    {
      "id": 123,
      "title": "2024年跨境电商发展趋势分析",
      "cover_image": "https://example.com/images/cover123.jpg",
      "publish_time": "2024-01-12T10:30:00Z",
      "view_count": 2856,
      "author": {
        "id": 5,
        "wechat_nickname": "电商专家",
        "author_name": "张三"
      }
    },
    {
      "id": 124,
      "title": "AI技术在电商中的应用实践",
      "cover_image": "https://example.com/images/cover124.jpg",
      "publish_time": "2024-01-11T14:20:00Z",
      "view_count": 1923,
      "author": {
        "id": 3,
        "wechat_nickname": "AI研究员",
        "author_name": "李四"
      }
    }
    // ... 更多文章
  ],
  "message": "获取周榜热门文章成功"
}
```

## 错误处理

```javascript
// 错误响应示例
{
  "success": false,
  "data": null,
  "message": "无效的榜单类型"
}
```

## 使用注意事项

1. **榜单类型**: 必须使用 `daily`、`weekly`、`monthly` 中的一个
2. **数量限制**: 最多返回20篇文章，建议使用默认的6篇
3. **时间范围**: 基于文章的发表时间和当前时间计算
4. **排序规则**: 按阅读量降序排列
5. **数据过滤**: 只返回已发布且可见的文章
6. **图片处理**: 建议添加默认图片和错误处理
7. **响应式设计**: 考虑移动端适配
