import logging
import uuid

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session, joinedload, load_only

from app.common.constants import UserType
from app.database import database
from app.model import models, schemas
from app.model.dependency import get_current_user
from app.oss import oss
import logging
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, File, UploadFile
from sqlalchemy.orm import Session
from typing import List
from app.model import models, schemas
from app.database import database
from sqlalchemy.orm import joinedload

from pydantic import BaseModel, Field, validator
from pydantic import BaseModel
from typing import Optional, Any
from app.oss import oss

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


router = APIRouter()


@router.post("/uploadfile/{product_id}",response_model=schemas.StandardResponse)
async def upload_file(product_id: str,file: UploadFile = File(...), db: Session = Depends(database.get_db),
                      current_user: models.User = Depends(get_current_user)):
    
    result = oss.upload_file_to_product_directory(product_id, file)
    sample = db.query(models.Sample).filter(models.Sample.id == product_id).first()
    if sample:
        sample.is_upload_script = 1
        db.commit()

    return schemas.StandardResponse(success=True, data={"status":result})
