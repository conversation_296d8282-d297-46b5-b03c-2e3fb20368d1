"""update_all_tables_to_utf8mb4

Revision ID: a1b2c3d4e5f6
Revises: 946f2be3582d
Create Date: 2024-07-29 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1b2c3d4e5f6'
down_revision = '946f2be3582d'
branch_labels = None
depends_on = None


def upgrade():
    # 修改所有表的字符集为utf8mb4
    tables = [
        'user',
        'role',
        'auth',
        'wechat_users',
        'qrcode_tickets',
        'guest_users',
        'link_groups',
        'site_configs',
        'image_tag_association',
        'tags',
        'images',
        'article_tag_association',
        'article_categories',
        'article_authors',
        'article_tags',
        'articles'
    ]
    
    for table in tables:
        op.execute(f"ALTER TABLE {table} CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        
    # 修改数据库默认字符集
    op.execute("ALTER DATABASE dny123 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci")


def downgrade():
    # 恢复到原来的字符集（utf8）
    tables = [
        'user',
        'role',
        'auth',
        'wechat_users',
        'qrcode_tickets',
        'guest_users',
        'link_groups',
        'site_configs',
        'image_tag_association',
        'tags',
        'images',
        'article_tag_association',
        'article_categories',
        'article_authors',
        'article_tags',
        'articles'
    ]
    
    for table in tables:
        op.execute(f"ALTER TABLE {table} CONVERT TO CHARACTER SET utf8 COLLATE utf8_general_ci")
        
    # 恢复数据库默认字符集
    op.execute("ALTER DATABASE dny123 CHARACTER SET = utf8 COLLATE = utf8_general_ci")
