"""make_site_config_datetime_nullable

Revision ID: 742c20a7d627
Revises: ca6ee6059cb1
Create Date: 2025-07-03 17:14:40.661346

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '742c20a7d627'
down_revision: Union[str, None] = 'ca6ee6059cb1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('site_configs', 'publish_time',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_comment='上架时间')
    op.alter_column('site_configs', 'offline_time',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_comment='下架时间')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('site_configs', 'offline_time',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_comment='下架时间')
    op.alter_column('site_configs', 'publish_time',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_comment='上架时间')
    # ### end Alembic commands ###
