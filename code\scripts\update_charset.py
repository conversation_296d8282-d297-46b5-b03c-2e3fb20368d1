#!/usr/bin/env python3
"""
数据库字符集更新脚本
将所有表的字符集从utf8更新为utf8mb4

使用方法:
python update_charset.py

注意: 执行前请备份数据库！
"""

import pymysql
import logging
from typing import List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-wz9kc1ko7s93t1exclo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'dny123',
    'password': 'Dny_123com',
    'database': 'dny123',
    'charset': 'utf8mb4'
}

# 需要更新的表列表
TABLES = [
    'user',
    'role', 
    'auth',
    'wechat_users',
    'qrcode_tickets',
    'guest_users',
    'link_groups',
    'site_configs',
    'image_tag_association',
    'tags',
    'images',
    'article_tag_association',
    'article_categories',
    'article_authors',
    'article_tags',
    'articles'
]

def get_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.info("数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def check_current_charset(connection):
    """检查当前数据库和表的字符集"""
    logger.info("检查当前字符集...")
    
    with connection.cursor() as cursor:
        # 检查数据库字符集
        cursor.execute("""
            SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
            FROM information_schema.SCHEMATA 
            WHERE SCHEMA_NAME = %s
        """, (DB_CONFIG['database'],))
        
        db_charset = cursor.fetchone()
        logger.info(f"数据库字符集: {db_charset}")
        
        # 检查表字符集
        cursor.execute("""
            SELECT TABLE_NAME, TABLE_COLLATION 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = %s 
            ORDER BY TABLE_NAME
        """, (DB_CONFIG['database'],))
        
        tables_charset = cursor.fetchall()
        logger.info("表字符集:")
        for table_name, collation in tables_charset:
            logger.info(f"  {table_name}: {collation}")

def backup_reminder():
    """备份提醒"""
    logger.warning("⚠️  重要提醒: 请确保已备份数据库！")
    logger.warning("⚠️  此操作将修改所有表的字符集，可能影响现有数据")
    
    response = input("是否继续执行字符集更新？(输入 'yes' 继续): ")
    if response.lower() != 'yes':
        logger.info("操作已取消")
        return False
    return True

def update_database_charset(connection):
    """更新数据库默认字符集"""
    logger.info("更新数据库默认字符集...")
    
    try:
        with connection.cursor() as cursor:
            sql = f"ALTER DATABASE {DB_CONFIG['database']} CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci"
            cursor.execute(sql)
            connection.commit()
            logger.info("✅ 数据库默认字符集更新成功")
    except Exception as e:
        logger.error(f"❌ 数据库字符集更新失败: {e}")
        raise

def update_table_charset(connection, table_name: str):
    """更新单个表的字符集"""
    logger.info(f"更新表 {table_name} 的字符集...")
    
    try:
        with connection.cursor() as cursor:
            sql = f"ALTER TABLE {table_name} CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
            cursor.execute(sql)
            connection.commit()
            logger.info(f"✅ 表 {table_name} 字符集更新成功")
    except Exception as e:
        logger.error(f"❌ 表 {table_name} 字符集更新失败: {e}")
        raise

def update_all_tables_charset(connection):
    """更新所有表的字符集"""
    logger.info("开始更新所有表的字符集...")
    
    success_count = 0
    failed_tables = []
    
    for table_name in TABLES:
        try:
            update_table_charset(connection, table_name)
            success_count += 1
        except Exception as e:
            failed_tables.append((table_name, str(e)))
            logger.error(f"表 {table_name} 更新失败，继续处理下一个表...")
    
    logger.info(f"字符集更新完成: 成功 {success_count}/{len(TABLES)} 个表")
    
    if failed_tables:
        logger.error("失败的表:")
        for table_name, error in failed_tables:
            logger.error(f"  {table_name}: {error}")
    
    return success_count, failed_tables

def verify_charset_update(connection):
    """验证字符集更新结果"""
    logger.info("验证字符集更新结果...")
    
    with connection.cursor() as cursor:
        # 验证数据库字符集
        cursor.execute("""
            SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
            FROM information_schema.SCHEMATA 
            WHERE SCHEMA_NAME = %s
        """, (DB_CONFIG['database'],))
        
        db_charset = cursor.fetchone()
        logger.info(f"数据库字符集: {db_charset}")
        
        # 验证表字符集
        cursor.execute("""
            SELECT TABLE_NAME, TABLE_COLLATION 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = %s 
            AND TABLE_NAME IN ({})
            ORDER BY TABLE_NAME
        """.format(','.join(['%s'] * len(TABLES))), [DB_CONFIG['database']] + TABLES)
        
        tables_charset = cursor.fetchall()
        
        utf8mb4_count = 0
        logger.info("表字符集验证结果:")
        for table_name, collation in tables_charset:
            is_utf8mb4 = 'utf8mb4' in collation
            status = "✅" if is_utf8mb4 else "❌"
            logger.info(f"  {status} {table_name}: {collation}")
            if is_utf8mb4:
                utf8mb4_count += 1
        
        logger.info(f"UTF8MB4字符集表数量: {utf8mb4_count}/{len(TABLES)}")
        
        return utf8mb4_count == len(TABLES)

def main():
    """主函数"""
    logger.info("🚀 开始数据库字符集更新")
    logger.info(f"目标数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
    logger.info(f"需要更新的表数量: {len(TABLES)}")
    
    # 备份提醒
    if not backup_reminder():
        return
    
    connection = None
    try:
        # 连接数据库
        connection = get_connection()
        
        # 检查当前字符集
        check_current_charset(connection)
        
        # 更新数据库字符集
        update_database_charset(connection)
        
        # 更新所有表的字符集
        success_count, failed_tables = update_all_tables_charset(connection)
        
        # 验证更新结果
        is_success = verify_charset_update(connection)
        
        if is_success and not failed_tables:
            logger.info("🎉 所有表的字符集更新成功！")
        else:
            logger.warning("⚠️ 部分表的字符集更新可能存在问题，请检查日志")
            
    except Exception as e:
        logger.error(f"❌ 字符集更新过程中发生错误: {e}")
    finally:
        if connection:
            connection.close()
            logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
