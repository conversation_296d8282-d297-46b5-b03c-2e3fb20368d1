#!/usr/bin/env python3
"""
测试微信图片防盗链处理功能
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'code'))

from app.utils.wechat_crawler import WeChatCrawler

def test_image_processing():
    """测试图片防盗链处理功能"""
    
    # 创建测试用的HTML内容，包含微信图片
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试微信图片防盗链</title>
    </head>
    <body>
        <div class="rich_media_area_primary">
            <h1 class="rich_media_title">测试图片防盗链处理</h1>
            <span class="rich_media_meta_text">测试作者</span>
            <em class="rich_media_meta_text">2024-01-01</em>
            
            <div class="rich_media_content" id="js_content">
                <p>这是一段包含微信图片的内容：</p>
                <img src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuQDr1Y8ly3yNlVR4nc68jBiaRz3IMp4UF0Y5EwSW1z4omWPtSLASn0NfvlCck0Zp3CHEuYkniaVnYDw/640?wx_fmt=png&from=appmsg" alt="测试图片">
                <p>这是另一张微信图片：</p>
                <img data-src="//mmbiz.qpic.cn/mmbiz_jpg/example/132?wx_fmt=jpeg" alt="小图片">
                <p>这是普通图片：</p>
                <img src="https://example.com/normal-image.jpg" alt="普通图片">
            </div>
        </div>
    </body>
    </html>
    """
    
    # 创建爬取器实例
    crawler = WeChatCrawler()
    
    try:
        # 提取文章内容
        result = crawler.extract_article_content(test_html)
        
        print("=" * 60)
        print("微信图片防盗链处理测试结果:")
        print("=" * 60)
        print(f"标题: {result['title']}")
        print(f"作者: {result['author']}")
        print("=" * 60)
        print("处理后的HTML内容:")
        print("=" * 60)
        print(result['content'])
        print("=" * 60)
        
        # 分析图片处理结果
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(result['content'], 'html.parser')
        images = soup.find_all('img')
        
        print(f"找到 {len(images)} 张图片:")
        for i, img in enumerate(images, 1):
            src = img.get('src', '')
            alt = img.get('alt', '')
            referrerpolicy = img.get('referrerpolicy', '')
            
            print(f"\n图片 {i}:")
            print(f"  Alt: {alt}")
            print(f"  Referrer Policy: {referrerpolicy}")
            print(f"  Source: {src[:100]}{'...' if len(src) > 100 else ''}")
            
            # 检查处理结果
            if 'mmbiz.qpic.cn' in src:
                if src.startswith('data:'):
                    print(f"  ✓ 已转换为Base64编码")
                elif 'proxy' in src:
                    print(f"  ✓ 使用了图片代理")
                elif referrerpolicy == 'no-referrer':
                    print(f"  ✓ 设置了no-referrer策略")
                else:
                    print(f"  ⚠ 可能仍有防盗链问题")
            else:
                print(f"  ✓ 非微信图片，无需特殊处理")
        
        # 保存测试结果
        with open('test_image_result.html', 'w', encoding='utf-8') as f:
            f.write(result['content'])
        print(f"\n✓ 测试结果已保存到 test_image_result.html")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试微信图片防盗链处理功能...")
    success = test_image_processing()
    
    if success:
        print("\n测试完成！")
        print("\n解决方案说明:")
        print("1. 添加了 referrerpolicy='no-referrer' 属性")
        print("2. 对小图片尝试转换为Base64编码")
        print("3. 支持配置图片代理服务")
        print("4. 使用特殊请求头获取微信图片")
    else:
        print("\n测试失败！")
