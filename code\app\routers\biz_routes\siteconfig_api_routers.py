from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from typing import List, Optional
from datetime import datetime, timezone
import logging
import json
from pydantic.json import pydantic_encoder

from app.model import schemas, models
from app.model.dependency import get_current_user
from app.database import database
from app.model.utils import HourliveException

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/create", response_model=schemas.StandardResponse)
async def create_site_config(
    site_config: schemas.SiteConfigCreate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    创建新的网站配置
    """
    try:
        # 创建网站配置
        db_site_config = models.SiteConfig(
            title=site_config.title,
            link_url=site_config.link_url,
            prompt_text=site_config.prompt_text,
            logo=site_config.logo,
            is_enabled=site_config.is_enabled,
            is_fixed_position=site_config.is_fixed_position,
            is_new_page=site_config.is_new_page,
            publish_time=site_config.publish_time,
            offline_time=site_config.offline_time,
            partner_code=site_config.partner_code,
            is_hover_enabled=site_config.is_hover_enabled,
            creator_id=current_user.id,
            updater_id=current_user.id
        )
        
        db.add(db_site_config)
        db.flush()  # 获取新创建的ID，但不提交事务
        
        # 处理连接组
        if site_config.link_groups:
            for link_group in site_config.link_groups:
                db_link_group = models.LinkGroup(
                    name=link_group.name,
                    url=link_group.url,
                    site_config_id=db_site_config.id,
                    creator_id=current_user.id,
                    updater_id=current_user.id
                )
                db.add(db_link_group)
        
        db.commit()
        db.refresh(db_site_config)
        
        # 构建返回数据，包含连接组
        result = schemas.SiteConfigReadWithLinks.from_orm(db_site_config)
        
        return schemas.StandardResponse(
            success=True,
            data=result,
            message="网站配置创建成功"
        )
        
    except Exception as e:
        db.rollback()
        raise HourliveException(message=f"创建网站配置失败: {str(e)}")

@router.get("/list", response_model=schemas.StandardResponse)
async def get_site_configs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页条数"),
    is_enabled: Optional[bool] = Query(None, description="启用状态筛选"),
    partner_code: Optional[str] = Query(None, description="合作伙伴代码筛选"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取网站配置列表（分页），包含连接组信息
    """
    try:
        # 构建查询
        query = db.query(models.SiteConfig)
        
        # 添加筛选条件
        if is_enabled is not None:
            query = query.filter(models.SiteConfig.is_enabled == is_enabled)
        if partner_code:
            query = query.filter(models.SiteConfig.partner_code == partner_code)
        
        # 获取总数
        total_count = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        site_configs = query.offset(offset).limit(page_size).all()
        
        # 构建响应数据
        response_data = [schemas.SiteConfigReadWithLinks.from_orm(config) for config in site_configs]
        
       
        
        # 将Pydantic模型转换为可序列化的字典
        response_dict = {
            "success": True,
            "data": [model.dict() for model in response_data],
            "total_records": total_count,
            "message": "获取网站配置列表成功"
        }
        
        # 打印JSON字符串
        json_str = json.dumps(response_dict, default=pydantic_encoder, ensure_ascii=False, indent=2)
        logger.info(f"SiteConfig list API response: {json_str}")
        
        # 返回响应
        return schemas.StandardResponse(
            success=True,
            data=response_data,
            total_records=total_count,
            message="获取网站配置列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取网站配置列表失败: {str(e)}")
        raise HourliveException(message=f"获取网站配置列表失败: {str(e)}")

@router.get("/{config_id}", response_model=schemas.StandardResponse)
async def get_site_config(
    config_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    根据ID获取网站配置详情
    """
    try:
        site_config = db.query(models.SiteConfig).filter(
            models.SiteConfig.id == config_id
        ).first()
        
        if not site_config:
            raise HourliveException(message="网站配置不存在")
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.SiteConfigReadWithLinks.from_orm(site_config),
            message="获取网站配置详情成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        raise HourliveException(message=f"获取网站配置详情失败: {str(e)}")

@router.put("/{config_id}", response_model=schemas.StandardResponse)
async def update_site_config(
    config_id: int,
    site_config_update: schemas.SiteConfigUpdate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    更新网站配置
    """
    try:
        # 检查SiteConfig是否存在
        db_site_config = db.query(models.SiteConfig).filter(
            models.SiteConfig.id == config_id
        ).first()
        
        if not db_site_config:
            raise HourliveException(message="网站配置不存在")
        
        # 更新基本字段
        update_data = site_config_update.dict(exclude_unset=True, exclude={"link_groups"})
        for field, value in update_data.items():
            setattr(db_site_config, field, value)
        
        # 更新操作人员
        db_site_config.updater_id = current_user.id
        
        # 处理连接组更新
        if site_config_update.link_groups is not None:
            # 删除现有的所有连接组
            db.query(models.LinkGroup).filter(
                models.LinkGroup.site_config_id == config_id
            ).delete()
            
            # 添加新的连接组
            for link_group in site_config_update.link_groups:
                db_link_group = models.LinkGroup(
                    name=link_group.name,
                    url=link_group.url,
                    site_config_id=config_id,
                    creator_id=current_user.id,
                    updater_id=current_user.id
                )
                db.add(db_link_group)
        
        db.commit()
        db.refresh(db_site_config)
        
        # 构建返回数据，包含连接组
        result = schemas.SiteConfigReadWithLinks.from_orm(db_site_config)
        
        return schemas.StandardResponse(
            success=True,
            data=result,
            message="网站配置更新成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        raise HourliveException(message=f"更新网站配置失败: {str(e)}")

@router.delete("/{config_id}", response_model=schemas.StandardResponse)
async def delete_site_config(
    config_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    删除网站配置
    """
    try:
        # 查找要删除的配置
        db_site_config = db.query(models.SiteConfig).filter(
            models.SiteConfig.id == config_id
        ).first()
        
        if not db_site_config:
            raise HourliveException(message="网站配置不存在")
        
        # 由于设置了cascade="all, delete-orphan"，删除SiteConfig时会自动删除关联的LinkGroup
        db.delete(db_site_config)
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            message="网站配置删除成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        raise HourliveException(message=f"删除网站配置失败: {str(e)}")

@router.post("/batch-delete", response_model=schemas.StandardResponse)
async def batch_delete_site_configs(
    config_ids: List[int],
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    批量删除网站配置
    """
    try:
        # 查找要删除的配置
        db_site_configs = db.query(models.SiteConfig).filter(
            models.SiteConfig.id.in_(config_ids)
        ).all()
        
        if not db_site_configs:
            raise HourliveException(message="没有找到要删除的网站配置")
        
        # 批量删除（关联的LinkGroup会自动删除）
        deleted_count = 0
        for config in db_site_configs:
            db.delete(config)
            deleted_count += 1
        
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            data={"deleted_count": deleted_count},
            message=f"成功删除{deleted_count}个网站配置"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        raise HourliveException(message=f"批量删除网站配置失败: {str(e)}")

@router.post("/toggle-status/{config_id}", response_model=schemas.StandardResponse)
async def toggle_site_config_status(
    config_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    切换网站配置启用状态
    """
    try:
        # 查找配置
        db_site_config = db.query(models.SiteConfig).filter(
            models.SiteConfig.id == config_id
        ).first()
        
        if not db_site_config:
            raise HourliveException(message="网站配置不存在")
        
        # 切换状态
        db_site_config.is_enabled = not db_site_config.is_enabled
        db_site_config.updater_id = current_user.id
        # 让SQLAlchemy自动处理update_time
        
        db.commit()
        db.refresh(db_site_config)
        
        status_text = "启用" if db_site_config.is_enabled else "禁用"
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.SiteConfigRead.from_orm(db_site_config),
            message=f"网站配置已{status_text}"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        raise HourliveException(message=f"切换网站配置状态失败: {str(e)}")  # 添加连接组相关的API路由
