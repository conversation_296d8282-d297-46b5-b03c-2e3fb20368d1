from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.database import database
from app.model import models, schemas
from app.model.dependency import get_current_user
from app.model.utils import HourliveException

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/create", response_model=schemas.StandardResponse)
async def create_article_author(
    author: schemas.ArticleAuthorCreate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """创建文章作者"""
    try:
        # 检查作者名是否已存在
        existing_author = db.query(models.ArticleAuthor).filter(
            models.ArticleAuthor.author_name == author.author_name
        ).first()
        
        if existing_author:
            raise HourliveException(message="作者名已存在")
        
        db_author = models.ArticleAuthor(
            **author.dict(),
            creator_id=current_user.id,
            updater_id=current_user.id
        )
        
        db.add(db_author)
        db.commit()
        db.refresh(db_author)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleAuthorRead.from_orm(db_author),
            message="文章作者创建成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建文章作者失败: {str(e)}")
        raise HourliveException(message=f"创建失败: {str(e)}")

@router.get("/list", response_model=schemas.StandardResponse)
async def get_article_authors(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页条数"),
    wechat_nickname: Optional[str] = Query(None, description="微信昵称模糊查询"),
    author_name: Optional[str] = Query(None, description="作者名模糊查询"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取文章作者列表"""
    try:
        query = db.query(models.ArticleAuthor)
        
        # 添加筛选条件
        if wechat_nickname:
            query = query.filter(models.ArticleAuthor.wechat_nickname.contains(wechat_nickname))
        if author_name:
            query = query.filter(models.ArticleAuthor.author_name.contains(author_name))
        
        # 获取总数
        total_count = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        authors = query.offset(offset).limit(page_size).all()
        
        return schemas.StandardResponse(
            success=True,
            data=[schemas.ArticleAuthorRead.from_orm(author) for author in authors],
            total_records=total_count,
            message="获取文章作者列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取文章作者列表失败: {str(e)}")
        raise HourliveException(message=f"获取失败: {str(e)}")

@router.get("/{author_id}", response_model=schemas.StandardResponse)
async def get_article_author(
    author_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取单个文章作者详情"""
    try:
        author = db.query(models.ArticleAuthor).filter(
            models.ArticleAuthor.id == author_id
        ).first()
        
        if not author:
            raise HourliveException(message="文章作者不存在")
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleAuthorRead.from_orm(author),
            message="获取文章作者详情成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        logger.error(f"获取文章作者详情失败: {str(e)}")
        raise HourliveException(message=f"获取失败: {str(e)}")

@router.put("/{author_id}", response_model=schemas.StandardResponse)
async def update_article_author(
    author_id: int,
    author_update: schemas.ArticleAuthorUpdate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """更新文章作者"""
    try:
        author = db.query(models.ArticleAuthor).filter(
            models.ArticleAuthor.id == author_id
        ).first()
        
        if not author:
            raise HourliveException(message="文章作者不存在")
        
        # 检查作者名是否已存在
        if author_update.author_name and author_update.author_name != author.author_name:
            existing_author = db.query(models.ArticleAuthor).filter(
                models.ArticleAuthor.author_name == author_update.author_name
            ).first()
            if existing_author:
                raise HourliveException(message="作者名已存在")
        
        # 更新字段
        update_data = author_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(author, field, value)
        
        author.updater_id = current_user.id
        
        db.commit()
        db.refresh(author)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleAuthorRead.from_orm(author),
            message="文章作者更新成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新文章作者失败: {str(e)}")
        raise HourliveException(message=f"更新失败: {str(e)}")

@router.delete("/{author_id}", response_model=schemas.StandardResponse)
async def delete_article_author(
    author_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """删除文章作者"""
    try:
        author = db.query(models.ArticleAuthor).filter(
            models.ArticleAuthor.id == author_id
        ).first()
        
        if not author:
            raise HourliveException(message="文章作者不存在")
        
        # 检查是否有文章使用此作者
        article_count = db.query(models.Article).filter(
            models.Article.author_id == author_id
        ).count()
        
        if article_count > 0:
            raise HourliveException(message=f"无法删除作者，还有 {article_count} 篇文章正在使用此作者")
        
        db.delete(author)
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            message="文章作者删除成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除文章作者失败: {str(e)}")
        raise HourliveException(message=f"删除失败: {str(e)}")