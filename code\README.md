## run
step1 创建python独立环境
```
cd server
python3 -m venv .
cd bin
source activate
```
step2 安装以来
```
pip3 install -r requirements.txt
```
step3 启动
```
uvicorn app.main:app --reload
```
step4 查看API & playground
```
http://127.0.0.1:8000/docs
```
## 目录结构
config   //配置
database //数据库工具
model    //模型，数据库和orm业务模型
    models //所有数据库模型
    schemas //所有orm
oss      //oss工具
routers  //各模块路由
funsar   //语音转文本
sumup    //摘要工具

## 数据库更新
alembic revision --autogenerate -m "Added email to User"
alembic upgrade head

alembic current 查看当前状态
alembic upgrade head 更新到最新数据库