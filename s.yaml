# ------------------------------------
#   官方手册: https://manual.serverless-devs.com/user-guide/aliyun/#fc3
#   有问题快来钉钉群问一下吧：33947367
# ------------------------------------

edition: 3.0.0
name: fastapi-app
access: 'undefined'
vars: 
  region: 'cn-hongkong'
resources:
  framework: 
    component: fc3
    actions: 
      pre-deploy: 
        - run: >-
            export PATH=/usr/local/envs/py310/bin:$PATH && pip3 install -r
            requirements.txt -t .
          path: ./code
    props: 
      region: ${vars.region} 
      description: Serverless Devs Web Framework Function
      runtime: custom.debian10
      timeout: 60
      environmentVariables:
        PATH: >-
          /var/fc/lang/python3.10/bin:/usr/local/bin/apache-maven/bin:/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/ruby/bin:/opt/bin:/code:/code/bin
      customRuntimeConfig:
        command:
          - python3
        args:
          - '-u'
          - index.py
      functionName: 'dny123comserver'
      code: ./code
  fc3_domain_0:
    component: fc3-domain
    props:
      region: ${vars.region}
      domainName: auto
      protocol: HTTP
      routeConfig:
        routes:
          - path: /*
            functionName: ${resources.framework.props.functionName}
