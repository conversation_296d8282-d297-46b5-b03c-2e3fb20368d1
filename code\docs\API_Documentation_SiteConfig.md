# 网站配置管理 API 接口文档

## 📋 概述

本文档描述了网站配置管理系统的 RESTful API 接口，提供完整的 CRUD 操作和状态管理功能。

### 基础信息
- **API 版本**: v1
- **基础路径**: `/siteconfig`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

---

## 🔐 认证说明

所有 API 接口都需要在请求头中包含有效的 Bearer Token：

```http
Authorization: Bearer {your_access_token}
Content-Type: application/json
```

---

## 📊 通用响应格式

所有接口都采用统一的响应格式：

```json
{
  "success": boolean,      // 操作是否成功
  "data": any,            // 返回的数据（可为空）
  "message": string,      // 提示信息
  "status_code": number,  // HTTP状态码，默认200
  "total_records": number // 分页查询时的总记录数
}
```

### 成功响应示例
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "status_code": 200
}
```

### 错误响应示例
```json
{
  "success": false,
  "message": "具体的错误信息",
  "status_code": 400
}
```

---

## 📋 数据模型

### SiteConfig 对象

| 字段名 | 类型 | 必填 | 描述 | 限制 |
|--------|------|------|------|------|
| id | integer | 否 | 配置ID（自动生成） | 只读 |
| title | string | 是 | 网站标题 | 最长255字符 |
| link_url | string | 是 | 标题链接URL | 最长255字符 |
| prompt_text | string | 是 | 提示内容文本 | 最长500字符 |
| logo | string | 是 | logo图片路径 | 最长255字符 |
| is_enabled | boolean | 否 | 是否启用 | 默认值：true |
| is_fixed_position | boolean | 否 | 是否固定位置 | 默认值：false |
| is_new_page | boolean | 否 | 是否跳转新页面 | 默认值：false |
| publish_time | datetime | 是 | 上架时间 | ISO 8601格式 |
| offline_time | datetime | 是 | 下架时间 | ISO 8601格式 |
| partner_code | string | 是 | 合作伙伴代码 | 最长100字符 |
| is_hover_enabled | boolean | 否 | hover是否启用 | 默认值：false |
| create_time | datetime | 否 | 创建时间 | 只读，自动生成 |
| update_time | datetime | 否 | 更新时间 | 只读，自动更新 |
| creator_id | integer | 否 | 创建人ID | 只读，自动填充 |
| updater_id | integer | 否 | 更新人ID | 只读，自动更新 |

### 完整对象示例
```json
{
  "id": 1,
  "title": "官网首页",
  "link_url": "https://www.example.com",
  "prompt_text": "欢迎访问我们的官方网站",
  "logo": "/images/logo.png",
  "is_enabled": true,
  "is_fixed_position": false,
  "is_new_page": true,
  "publish_time": "2024-01-01T00:00:00Z",
  "offline_time": "2024-12-31T23:59:59Z",
  "partner_code": "PARTNER001",
  "is_hover_enabled": true,
  "create_time": "2024-01-01T10:00:00Z",
  "update_time": "2024-01-01T10:00:00Z",
  "creator_id": 100,
  "updater_id": 100
}
```

---

## 🔗 API 接口详情

### 1. 创建网站配置

**接口地址**: `POST /siteconfig/create`

**描述**: 创建新的网站配置记录

#### 请求参数

```json
{
  "title": "网站标题",
  "link_url": "https://example.com",
  "prompt_text": "提示内容文本",
  "logo": "/images/logo.png",
  "is_enabled": true,
  "is_fixed_position": false,
  "is_new_page": false,
  "publish_time": "2024-01-01T00:00:00Z",
  "offline_time": "2024-12-31T23:59:59Z",
  "partner_code": "PARTNER001",
  "is_hover_enabled": false
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "网站标题",
    "link_url": "https://example.com",
    "prompt_text": "提示内容文本",
    "logo": "/images/logo.png",
    "is_enabled": true,
    "is_fixed_position": false,
    "is_new_page": false,
    "publish_time": "2024-01-01T00:00:00Z",
    "offline_time": "2024-12-31T23:59:59Z",
    "partner_code": "PARTNER001",
    "is_hover_enabled": false,
    "create_time": "2024-01-01T10:00:00Z",
    "update_time": "2024-01-01T10:00:00Z",
    "creator_id": 100,
    "updater_id": 100
  },
  "message": "网站配置创建成功"
}
```

#### cURL 示例
```bash
curl -X POST "/siteconfig/create" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "网站标题",
    "link_url": "https://example.com",
    "prompt_text": "提示内容文本",
    "logo": "/images/logo.png",
    "publish_time": "2024-01-01T00:00:00Z",
    "offline_time": "2024-12-31T23:59:59Z",
    "partner_code": "PARTNER001"
  }'
```

---

### 2. 获取网站配置列表

**接口地址**: `GET /siteconfig/list`

**描述**: 分页获取网站配置列表，支持筛选条件

#### 查询参数

| 参数名 | 类型 | 必填 | 描述 | 默认值 | 限制 |
|--------|------|------|------|--------|------|
| page | integer | 否 | 页码 | 1 | 最小值：1 |
| page_size | integer | 否 | 每页条数 | 10 | 范围：1-100 |
| is_enabled | boolean | 否 | 启用状态筛选 | - | true/false |
| partner_code | string | 否 | 合作伙伴代码筛选 | - | - |

#### 请求示例
```http
GET /siteconfig/list?page=1&page_size=20&is_enabled=true&partner_code=PARTNER001
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "网站标题1",
      "link_url": "https://example1.com",
      "prompt_text": "提示内容1",
      "logo": "/images/logo1.png",
      "is_enabled": true,
      "is_fixed_position": false,
      "is_new_page": false,
      "publish_time": "2024-01-01T00:00:00Z",
      "offline_time": "2024-12-31T23:59:59Z",
      "partner_code": "PARTNER001",
      "is_hover_enabled": false,
      "create_time": "2024-01-01T10:00:00Z",
      "update_time": "2024-01-01T10:00:00Z",
      "creator_id": 100,
      "updater_id": 100
    }
  ],
  "total_records": 150,
  "message": "获取网站配置列表成功"
}
```

#### cURL 示例
```bash
curl -X GET "/siteconfig/list?page=1&page_size=10&is_enabled=true" \
  -H "Authorization: Bearer your_token"
```

---

### 3. 获取单个网站配置

**接口地址**: `GET /siteconfig/{config_id}`

**描述**: 根据配置ID获取单个网站配置的详细信息

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| config_id | integer | 是 | 网站配置ID |

#### 请求示例
```http
GET /siteconfig/123
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "title": "网站标题",
    "link_url": "https://example.com",
    "prompt_text": "提示内容文本",
    "logo": "/images/logo.png",
    "is_enabled": true,
    "is_fixed_position": false,
    "is_new_page": false,
    "publish_time": "2024-01-01T00:00:00Z",
    "offline_time": "2024-12-31T23:59:59Z",
    "partner_code": "PARTNER001",
    "is_hover_enabled": false,
    "create_time": "2024-01-01T10:00:00Z",
    "update_time": "2024-01-01T10:00:00Z",
    "creator_id": 100,
    "updater_id": 100
  },
  "message": "获取网站配置详情成功"
}
```

**错误响应 (404)**:
```json
{
  "success": false,
  "message": "网站配置不存在",
  "status_code": 404
}
```

#### cURL 示例
```bash
curl -X GET "/siteconfig/123" \
  -H "Authorization: Bearer your_token"
```

---

### 4. 更新网站配置

**接口地址**: `PUT /siteconfig/{config_id}`

**描述**: 更新指定ID的网站配置信息

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| config_id | integer | 是 | 网站配置ID |

#### 请求参数

> 注意：所有字段都是可选的，只需传入需要更新的字段

```json
{
  "title": "更新后的标题",
  "is_enabled": false,
  "publish_time": "2024-02-01T00:00:00Z",
  "partner_code": "PARTNER002"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "title": "更新后的标题",
    "link_url": "https://example.com",
    "prompt_text": "提示内容文本",
    "logo": "/images/logo.png",
    "is_enabled": false,
    "is_fixed_position": false,
    "is_new_page": false,
    "publish_time": "2024-02-01T00:00:00Z",
    "offline_time": "2024-12-31T23:59:59Z",
    "partner_code": "PARTNER002",
    "is_hover_enabled": false,
    "create_time": "2024-01-01T10:00:00Z",
    "update_time": "2024-01-15T14:30:00Z",
    "creator_id": 100,
    "updater_id": 105
  },
  "message": "网站配置更新成功"
}
```

#### cURL 示例
```bash
curl -X PUT "/siteconfig/123" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的标题",
    "is_enabled": false
  }'
```

---

### 5. 删除网站配置

**接口地址**: `DELETE /siteconfig/{config_id}`

**描述**: 删除指定ID的网站配置

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| config_id | integer | 是 | 网站配置ID |

#### 请求示例
```http
DELETE /siteconfig/123
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "网站配置删除成功"
}
```

**错误响应 (404)**:
```json
{
  "success": false,
  "message": "网站配置不存在",
  "status_code": 404
}
```

#### cURL 示例
```bash
curl -X DELETE "/siteconfig/123" \
  -H "Authorization: Bearer your_token"
```

---

### 6. 批量删除网站配置

**接口地址**: `POST /siteconfig/batch-delete`

**描述**: 批量删除多个网站配置

#### 请求参数

传入配置ID数组：

```json
[1, 2, 3, 4, 5]
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "deleted_count": 5
  },
  "message": "成功删除5个网站配置"
}
```

**部分成功响应**:
```json
{
  "success": true,
  "data": {
    "deleted_count": 3
  },
  "message": "成功删除3个网站配置"
}
```

#### cURL 示例
```bash
curl -X POST "/siteconfig/batch-delete" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '[1, 2, 3, 4, 5]'
```

---

### 7. 切换启用状态

**接口地址**: `POST /siteconfig/toggle-status/{config_id}`

**描述**: 切换指定网站配置的启用/禁用状态

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| config_id | integer | 是 | 网站配置ID |

#### 请求示例
```http
POST /siteconfig/toggle-status/123
```

#### 响应示例

**启用状态响应 (200)**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "title": "网站标题",
    "link_url": "https://example.com",
    "prompt_text": "提示内容文本",
    "logo": "/images/logo.png",
    "is_enabled": true,
    "is_fixed_position": false,
    "is_new_page": false,
    "publish_time": "2024-01-01T00:00:00Z",
    "offline_time": "2024-12-31T23:59:59Z",
    "partner_code": "PARTNER001",
    "is_hover_enabled": false,
    "create_time": "2024-01-01T10:00:00Z",
    "update_time": "2024-01-15T14:30:00Z",
    "creator_id": 100,
    "updater_id": 105
  },
  "message": "网站配置已启用"
}
```

#### cURL 示例
```bash
curl -X POST "/siteconfig/toggle-status/123" \
  -H "Authorization: Bearer your_token"
```

---

## ⚠️ 错误码说明

| HTTP状态码 | 描述 | 常见原因 |
|------------|------|----------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数格式错误、必填参数缺失 |
| 401 | 未授权 | Token无效或已过期 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 指定的配置ID不存在 |
| 422 | 参数验证失败 | 参数值不符合验证规则 |
| 500 | 服务器内部错误 | 系统异常 |

---

## 🛠️ 前端集成指南

### JavaScript/TypeScript 示例

#### 1. 基础配置
```typescript
const API_BASE_URL = 'https://your-api-domain.com';
const API_PREFIX = '/siteconfig';

// 请求拦截器
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 添加认证令牌
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

#### 2. API 方法封装
```typescript
interface SiteConfig {
  id?: number;
  title: string;
  link_url: string;
  prompt_text: string;
  logo: string;
  is_enabled?: boolean;
  is_fixed_position?: boolean;
  is_new_page?: boolean;
  publish_time: string;
  offline_time: string;
  partner_code: string;
  is_hover_enabled?: boolean;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  status_code?: number;
  total_records?: number;
}

interface ListParams {
  page?: number;
  page_size?: number;
  is_enabled?: boolean;
  partner_code?: string;
}

class SiteConfigAPI {
  // 创建配置
  static async create(data: Omit<SiteConfig, 'id'>): Promise<ApiResponse<SiteConfig>> {
    const response = await apiClient.post(`${API_PREFIX}/create`, data);
    return response.data;
  }

  // 获取列表
  static async getList(params: ListParams = {}): Promise<ApiResponse<SiteConfig[]>> {
    const response = await apiClient.get(`${API_PREFIX}/list`, { params });
    return response.data;
  }

  // 获取详情
  static async getById(id: number): Promise<ApiResponse<SiteConfig>> {
    const response = await apiClient.get(`${API_PREFIX}/${id}`);
    return response.data;
  }

  // 更新配置
  static async update(id: number, data: Partial<SiteConfig>): Promise<ApiResponse<SiteConfig>> {
    const response = await apiClient.put(`${API_PREFIX}/${id}`, data);
    return response.data;
  }

  // 删除配置
  static async delete(id: number): Promise<ApiResponse<null>> {
    const response = await apiClient.delete(`${API_PREFIX}/${id}`);
    return response.data;
  }

  // 批量删除
  static async batchDelete(ids: number[]): Promise<ApiResponse<{deleted_count: number}>> {
    const response = await apiClient.post(`${API_PREFIX}/batch-delete`, ids);
    return response.data;
  }

  // 切换状态
  static async toggleStatus(id: number): Promise<ApiResponse<SiteConfig>> {
    const response = await apiClient.post(`${API_PREFIX}/toggle-status/${id}`);
    return response.data;
  }
}
```

#### 3. React Hook 示例
```typescript
import { useState, useEffect } from 'react';

// 自定义Hook：网站配置列表
export const useSiteConfigList = (params: ListParams = {}) => {
  const [data, setData] = useState<SiteConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await SiteConfigAPI.getList(params);
      
      if (response.success) {
        setData(response.data);
        setTotal(response.total_records || 0);
      } else {
        setError(response.message);
      }
    } catch (err) {
      setError('获取数据失败');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [JSON.stringify(params)]);

  return {
    data,
    loading,
    total,
    error,
    refresh: fetchData,
  };
};

// 自定义Hook：网站配置详情
export const useSiteConfig = (id: number) => {
  const [data, setData] = useState<SiteConfig | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const response = await SiteConfigAPI.getById(id);
      
      if (response.success) {
        setData(response.data);
      } else {
        setError(response.message);
      }
    } catch (err) {
      setError('获取数据失败');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [id]);

  return {
    data,
    loading,
    error,
    refresh: fetchData,
  };
};
```

#### 4. 表单验证规则
```typescript
import { z } from 'zod';

export const siteConfigSchema = z.object({
  title: z.string()
    .min(1, '标题不能为空')
    .max(255, '标题长度不能超过255个字符'),
  
  link_url: z.string()
    .min(1, '链接URL不能为空')
    .max(255, 'URL长度不能超过255个字符')
    .url('请输入有效的URL格式'),
  
  prompt_text: z.string()
    .min(1, '提示文本不能为空')
    .max(500, '提示文本长度不能超过500个字符'),
  
  logo: z.string()
    .min(1, 'Logo路径不能为空')
    .max(255, 'Logo路径长度不能超过255个字符'),
  
  partner_code: z.string()
    .min(1, '合作伙伴代码不能为空')
    .max(100, '合作伙伴代码长度不能超过100个字符'),
  
  publish_time: z.string()
    .min(1, '上架时间不能为空')
    .refine((val) => !isNaN(Date.parse(val)), '请输入有效的日期格式'),
  
  offline_time: z.string()
    .min(1, '下架时间不能为空')
    .refine((val) => !isNaN(Date.parse(val)), '请输入有效的日期格式'),
  
  is_enabled: z.boolean().optional(),
  is_fixed_position: z.boolean().optional(),
  is_new_page: z.boolean().optional(),
  is_hover_enabled: z.boolean().optional(),
});

export type SiteConfigFormData = z.infer<typeof siteConfigSchema>;
```

---

## 🔍 测试用例

### Postman 集合示例

```json
{
  "info": {
    "name": "网站配置管理 API",
    "description": "网站配置管理系统的完整API测试集合"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "https://your-api-domain.com"
    },
    {
      "key": "token",
      "value": "your_access_token"
    }
  ],
  "item": [
    {
      "name": "创建网站配置",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{token}}"
          },
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "url": "{{baseUrl}}/siteconfig/create",
        "body": {
          "mode": "raw",
          "raw": "{\n  \"title\": \"测试配置\",\n  \"link_url\": \"https://test.com\",\n  \"prompt_text\": \"这是一个测试配置\",\n  \"logo\": \"/images/test-logo.png\",\n  \"publish_time\": \"2024-01-01T00:00:00Z\",\n  \"offline_time\": \"2024-12-31T23:59:59Z\",\n  \"partner_code\": \"TEST001\"\n}"
        }
      }
    }
  ]
}
```

---

## 📝 更新日志

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v1.0.0 | 2024-01-01 | 初始版本，包含基础CRUD操作 |
| v1.1.0 | 2024-01-15 | 新增批量删除和状态切换功能 |
| v1.2.0 | 2024-02-01 | 优化分页查询，增加筛选条件 |

---

## 📞 技术支持

如果在使用过程中遇到问题，请联系开发团队：

- **邮箱**: <EMAIL>
- **文档**: https://docs.company.com/api
- **问题反馈**: https://github.com/company/issues

---

**文档最后更新时间**: 2024-01-15  
**API 版本**: v1.2.0