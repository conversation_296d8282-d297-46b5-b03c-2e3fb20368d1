from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.database import database
from app.model import models, schemas
from app.model.dependency import get_current_user
from app.model.utils import HourliveException

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/create", response_model=schemas.StandardResponse)
async def create_article_category(
    category: schemas.ArticleCategoryCreate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """创建标签分类"""
    try:
        # 检查分类名是否已存在
        existing_category = db.query(models.ArticleCategory).filter(
            models.ArticleCategory.name == category.name
        ).first()
        
        if existing_category:
            raise HourliveException(message="分类名称已存在")
        
        db_category = models.ArticleCategory(
            **category.dict(),
            creator_id=current_user.id,
            updater_id=current_user.id
        )
        
        db.add(db_category)
        db.commit()
        db.refresh(db_category)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleCategoryRead.from_orm(db_category),
            message="标签分类创建成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建标签分类失败: {str(e)}")
        raise HourliveException(message=f"创建失败: {str(e)}")

@router.get("/list", response_model=schemas.StandardResponse)
async def get_article_categories(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页条数"),
    name: Optional[str] = Query(None, description="分类名称模糊查询"),
    weight: Optional[str] = Query(None, description="分类权重精确查询"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取标签分类列表"""
    try:
        query = db.query(models.ArticleCategory)
        
        # 添加筛选条件
        if name:
            query = query.filter(models.ArticleCategory.name.contains(name))
        if weight:
            query = query.filter(models.ArticleCategory.weight == weight)
        
        # 获取总数
        total_count = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        categories = query.offset(offset).limit(page_size).all()
        
        return schemas.StandardResponse(
            success=True,
            data=[schemas.ArticleCategoryRead.from_orm(category) for category in categories],
            total_records=total_count,
            message="获取标签分类列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取标签分类列表失败: {str(e)}")
        raise HourliveException(message=f"获取失败: {str(e)}")

@router.get("/{category_id}", response_model=schemas.StandardResponse)
async def get_article_category(
    category_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取单个标签分类详情"""
    try:
        category = db.query(models.ArticleCategory).filter(
            models.ArticleCategory.id == category_id
        ).first()
        
        if not category:
            raise HourliveException(message="标签分类不存在")
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleCategoryRead.from_orm(category),
            message="获取标签分类详情成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        logger.error(f"获取标签分类详情失败: {str(e)}")
        raise HourliveException(message=f"获取失败: {str(e)}")

@router.put("/{category_id}", response_model=schemas.StandardResponse)
async def update_article_category(
    category_id: int,
    category_update: schemas.ArticleCategoryUpdate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """更新标签分类"""
    try:
        category = db.query(models.ArticleCategory).filter(
            models.ArticleCategory.id == category_id
        ).first()
        
        if not category:
            raise HourliveException(message="标签分类不存在")
        
        # 检查分类名是否已存在
        if category_update.name and category_update.name != category.name:
            existing_category = db.query(models.ArticleCategory).filter(
                models.ArticleCategory.name == category_update.name
            ).first()
            if existing_category:
                raise HourliveException(message="分类名称已存在")
        
        # 更新字段
        update_data = category_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(category, field, value)
        
        category.updater_id = current_user.id
        
        db.commit()
        db.refresh(category)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleCategoryRead.from_orm(category),
            message="标签分类更新成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新标签分类失败: {str(e)}")
        raise HourliveException(message=f"更新失败: {str(e)}")

@router.delete("/{category_id}", response_model=schemas.StandardResponse)
async def delete_article_category(
    category_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """删除标签分类"""
    try:
        category = db.query(models.ArticleCategory).filter(
            models.ArticleCategory.id == category_id
        ).first()
        
        if not category:
            raise HourliveException(message="标签分类不存在")
        
        # 检查是否有标签使用此分类
        tag_count = db.query(models.ArticleTag).filter(
            models.ArticleTag.category_id == category_id
        ).count()
        
        if tag_count > 0:
            raise HourliveException(message=f"无法删除分类，还有 {tag_count} 个标签正在使用此分类")
        
        db.delete(category)
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            message="标签分类删除成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除标签分类失败: {str(e)}")
        raise HourliveException(message=f"删除失败: {str(e)}")