import oss2
import os
from oss2.credentials import EnvironmentVariableCredentialsProvider
from fastapi import UploadFile
import uuid
from PIL import Image
import io

access_key_id: str = "LTAI5tDVJ34NAWssovrFbjKY"
access_key_secret: str = "******************************"
bucket_name: str = "images-xl"
endpoint: str = "https://oss-cn-beijing.aliyuncs.com"

auth = oss2.Auth(access_key_id, access_key_secret)
bucket = oss2.Bucket(auth, endpoint, bucket_name, region='cn-beijing')

def upload_avatar(uid, file:UploadFile):
    """
    上传头像
    """
    object_name = f'avatar/{uid}/avatar/avatar.png'
    result = bucket.put_object(object_name, file.file)
    return result.status

def upload_streamer_avatar(uid, file:UploadFile):
    """
    上传主播头像
    """
    object_name = f'streamer/{uid}/avatar/avatar.png'
    result = bucket.put_object(object_name, file.file)
    return result.status

def upload_file_to_product_directory(product_id, file: UploadFile):
    """
    上传文件到指定的product_id目录

    :param product_id: 产品ID，对应OSS中的目录
    :param file: 上传的文件
    :return: None
    """
    object_name = f'{product_id}/{file.filename}'
    result = bucket.put_object(object_name, file.file)
    return result.status

def list_files_in_product_directory(product_id):
    """
    获取指定product_id目录下的文件列表

    :param product_id: 产品ID，对应OSS中的目录
    :return: 文件列表
    """
    prefix = f'{product_id}/'
    file_list = []

    for obj in oss2.ObjectIterator(bucket, prefix=prefix):
        print(obj.key)
        file_list.append({'name': os.path.basename(obj.key),"path":bucket.sign_url('GET', obj.key, 3600,params={'response-content-disposition': 'attachment'})})
    return file_list

def delete_file_in_product_directory(product_id, file_name):
    """
    删除指定product_id目录下的文件

    :param product_id: 产品ID，对应OSS中的目录
    :param file_name: 文件名
    :return: None
    """
    object_name = f'{product_id}/{file_name}'
    result = bucket.delete_object(object_name)
    return result.status

def upload_file_to_directory(path,file: UploadFile):
    """
    上传文件到avatar目录

    :param file: 上传的文件
    :return: None
    """
    object_name = f'{path}/{file.filename}'
    result = bucket.put_object(object_name, file.file)
    return result.status

def upload_image_to_images_directory(file: UploadFile):
    """
    上传图片到images目录，生成唯一文件名

    :param file: 上传的图片文件
    :return: dict 包含文件信息
    """
    # 生成唯一的文件名
    file_extension = file.filename.split('.')[-1].lower() if '.' in file.filename else ''
    new_filename = f"{uuid.uuid4()}.{file_extension}"
    object_name = f'images/{new_filename}'
    # object_name = new_filename
    
    # 读取文件内容用于获取图片尺寸
    file_content = file.file.read()
    file.file.seek(0)  # 重置文件指针
    
    # 获取图片尺寸信息
    width, height = None, None
    try:
        if file_extension.lower() in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
            image = Image.open(io.BytesIO(file_content))
            width, height = image.size
    except Exception:
        pass  # 如果无法获取尺寸信息，则忽略
    
    # 上传到OSS
    result = bucket.put_object(object_name, io.BytesIO(file_content))
    
    # 生成访问URL（有效期1年）
    access_url = bucket.sign_url('GET', object_name, 3600 * 24 * 365)
    
    return {
        'status': result.status,
        'original_name': file.filename,
        'new_name': new_filename,
        'access_url': access_url,
        'file_size': len(file_content),
        'file_type': file_extension,
        'width': width,
        'height': height,
        'object_name': object_name
    }

def delete_image_from_images_directory(new_name: str):
    """
    从images目录删除图片

    :param new_name: 图片的新名称（存储名）
    :return: 删除状态
    """
    object_name = f'images/{new_name}'
    result = bucket.delete_object(object_name)
    return result.status

def get_image_signed_url(new_name: str, expire_seconds: int = 3600):
    """
    获取图片的签名访问URL

    :param new_name: 图片的新名称（存储名）
    :param expire_seconds: URL有效期（秒）
    :return: 签名URL
    """
    object_name = f'images/{new_name}'
    return bucket.sign_url('GET', object_name, expire_seconds)