# 用户端文章查询API文档

## 概述

用户端文章查询API提供了面向前端用户的文章查询功能，**无需用户登录认证**，主要用于网站前台展示文章内容。

## 特点

- ✅ **无需认证**: 所有接口都不需要用户登录
- ✅ **安全过滤**: 只返回已发布且可见的文章
- ✅ **性能优化**: 支持分页查询，避免大量数据传输
- ✅ **多维筛选**: 支持按频道、标签、作者、关键词筛选
- ✅ **自动统计**: 自动增加文章浏览量

## API接口列表

### 📋 文章列表查询

**接口地址**: `GET /public-article/list`

**功能**: 获取文章列表，按发表时间倒序排序

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页条数，默认10，最大50 |
| channel | string | 否 | 频道筛选（头条、百科、快讯） |
| tag_id | int | 否 | 标签ID筛选 |
| author_id | int | 否 | 作者ID筛选 |
| keyword | string | 否 | 关键词搜索（标题、简介） |

**返回字段**:
- `id`: 文章ID
- `title`: 文章标题
- `cover_image`: 封面图URL
- `summary`: 文章简介
- `publish_time`: 发表时间
- `author`: 作者信息
  - `id`: 作者ID
  - `wechat_nickname`: 微信昵称
  - `author_name`: 作者名
- `tags`: 标签列表
  - `id`: 标签ID
  - `name`: 标签名

**请求示例**:
```bash
# 获取第一页文章
curl -X GET "http://localhost:9000/public-article/list?page=1&page_size=10"

# 按频道筛选
curl -X GET "http://localhost:9000/public-article/list?channel=头条"

# 按标签筛选
curl -X GET "http://localhost:9000/public-article/list?tag_id=1"

# 关键词搜索
curl -X GET "http://localhost:9000/public-article/list?keyword=跨境电商"

# 组合筛选
curl -X GET "http://localhost:9000/public-article/list?page=1&page_size=5&channel=头条&keyword=电商&tag_id=2"
```

**返回示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "title": "2024年跨境电商发展趋势分析",
      "cover_image": "https://example.com/images/cover123.jpg",
      "summary": "本文深入分析了2024年跨境电商行业的最新发展趋势，包括市场规模、技术创新、政策变化等多个维度。",
      "publish_time": "2024-01-15T10:30:00Z",
      "author": {
        "id": 5,
        "wechat_nickname": "电商专家",
        "author_name": "张三"
      },
      "tags": [
        {
          "id": 1,
          "name": "跨境电商"
        },
        {
          "id": 2,
          "name": "行业分析"
        }
      ]
    },
    {
      "id": 124,
      "title": "AI技术在电商中的应用实践",
      "cover_image": "https://example.com/images/cover124.jpg",
      "summary": "探讨人工智能技术如何革新传统电商模式，提升用户体验和运营效率。",
      "publish_time": "2024-01-14T14:20:00Z",
      "author": {
        "id": 3,
        "wechat_nickname": "AI研究员",
        "author_name": "李四"
      },
      "tags": [
        {
          "id": 3,
          "name": "人工智能"
        }
      ]
    }
  ],
  "total_records": 25,
  "message": "获取文章列表成功"
}
```

### 📄 文章详情查询

**接口地址**: `GET /public-article/detail/{article_id}`

**功能**: 获取文章详细内容，自动增加浏览量

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| article_id | int | 是 | 文章ID |

**返回字段**:
- 包含列表接口的所有字段
- `content`: 文章主体内容
- `style`: 文章风格（富文本/markdown）
- `view_count`: 浏览量
- `like_count`: 点赞数
- `favorite_count`: 收藏数
- `comment_count`: 评论数
- `author`: 作者信息（简化版）
  - `id`: 作者ID
  - `wechat_nickname`: 微信昵称
  - `author_name`: 作者名
- `tags`: 标签列表（简化版）
  - `id`: 标签ID
  - `name`: 标签名

**请求示例**:
```bash
# 获取文章详情
curl -X GET "http://localhost:9000/public-article/detail/123"
```

**返回示例**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "title": "2024年跨境电商发展趋势分析",
    "cover_image": "https://example.com/images/cover123.jpg",
    "summary": "本文深入分析了2024年跨境电商行业的最新发展趋势，包括市场规模、技术创新、政策变化等多个维度。",
    "content": "<h1>2024年跨境电商发展趋势分析</h1><p>随着全球数字化进程的加速，跨境电商行业正迎来前所未有的发展机遇...</p><h2>市场规模分析</h2><p>根据最新数据显示，2024年全球跨境电商市场规模预计将达到...</p>",
    "style": "富文本",
    "publish_time": "2024-01-15T10:30:00Z",
    "view_count": 1256,
    "like_count": 89,
    "favorite_count": 45,
    "comment_count": 23,
    "author": {
      "id": 5,
      "wechat_nickname": "电商专家",
      "author_name": "张三"
    },
    "tags": [
      {
        "id": 1,
        "name": "跨境电商"
      },
      {
        "id": 2,
        "name": "行业分析"
      }
    ]
  },
  "message": "获取文章详情成功"
}
```

**错误返回示例**:
```json
{
  "success": false,
  "data": null,
  "message": "文章不存在或未发布"
}
```

### 📺 频道列表查询

**接口地址**: `GET /public-article/channels`

**功能**: 获取所有有文章的频道及其文章数量

**返回字段**:
- `channel`: 频道名称
- `article_count`: 该频道的文章数量

**请求示例**:
```bash
curl -X GET "http://localhost:9000/public-article/channels"
```

**返回示例**:
```json
{
  "success": true,
  "data": [
    {
      "channel": "头条",
      "article_count": 15
    },
    {
      "channel": "百科",
      "article_count": 8
    },
    {
      "channel": "快讯",
      "article_count": 12
    }
  ],
  "message": "获取频道列表成功"
}
```

### 🏷️ 标签列表查询

**接口地址**: `GET /public-article/tags`

**功能**: 获取所有有文章关联的标签

**返回字段**:
- `id`: 标签ID
- `name`: 标签名
- `category_id`: 标签分类ID
- `create_time`: 创建时间
- `update_time`: 更新时间
- `creator_id`: 创建者ID
- `updater_id`: 更新者ID
- `article_count`: 使用该标签的文章数量

**请求示例**:
```bash
curl -X GET "http://localhost:9000/public-article/tags"
```

**返回示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "跨境电商",
      "category_id": 1,
      "create_time": "2023-12-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1,
      "article_count": 8
    },
    {
      "id": 2,
      "name": "行业分析",
      "category_id": 1,
      "create_time": "2023-12-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1,
      "article_count": 5
    },
    {
      "id": 3,
      "name": "人工智能",
      "category_id": 2,
      "create_time": "2023-12-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1,
      "article_count": 3
    }
  ],
  "message": "获取标签列表成功"
}
```

### 🔥 热门文章查询

**接口地址**: `GET /public-article/hot`

**功能**: 获取热门文章榜单，按阅读量排序

### 👥 作者列表查询

**接口地址**: `GET /public-article/authors`

**功能**: 获取所有有文章的作者及其文章数量

**返回字段**:
- `id`: 作者ID
- `wechat_nickname`: 微信昵称
- `author_name`: 作者名
- `create_time`: 创建时间
- `update_time`: 更新时间
- `creator_id`: 创建者ID
- `updater_id`: 更新者ID
- `article_count`: 该作者的文章数量

**请求示例**:
```bash
curl -X GET "http://localhost:9000/public-article/authors"
```

**返回示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 5,
      "wechat_nickname": "电商专家",
      "author_name": "张三",
      "create_time": "2023-12-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1,
      "article_count": 12
    },
    {
      "id": 3,
      "wechat_nickname": "AI研究员",
      "author_name": "李四",
      "create_time": "2023-12-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1,
      "article_count": 8
    },
    {
      "id": 7,
      "wechat_nickname": "科技观察者",
      "author_name": "王五",
      "create_time": "2023-12-01T00:00:00Z",
      "update_time": "2024-01-01T00:00:00Z",
      "creator_id": 1,
      "updater_id": 1,
      "article_count": 5
    }
  ],
  "message": "获取作者列表成功"
}
```

### 🔥 热门文章查询

**接口地址**: `GET /public-article/hot`

**功能**: 获取热门文章榜单，按阅读量排序

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| period | string | 是 | 榜单类型：daily-日榜，weekly-周榜，monthly-月榜 |
| limit | int | 否 | 返回文章数量，默认6篇，最多20篇 |

**返回字段**:
直接返回文章数组，每个文章对象包含：
- `id`: 文章ID
- `title`: 文章标题
- `cover_image`: 封面图URL
- `publish_time`: 发表时间
- `view_count`: 阅读量
- `author`: 作者信息（简化版）
  - `id`: 作者ID
  - `wechat_nickname`: 微信昵称
  - `author_name`: 作者名

**请求示例**:
```bash
# 获取日榜前6篇热门文章
curl -X GET "http://localhost:9000/public-article/hot?period=daily"

# 获取周榜前10篇热门文章
curl -X GET "http://localhost:9000/public-article/hot?period=weekly&limit=10"

# 获取月榜前6篇热门文章
curl -X GET "http://localhost:9000/public-article/hot?period=monthly"
```

**返回示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "title": "2024年跨境电商发展趋势分析",
      "cover_image": "https://example.com/images/cover123.jpg",
      "publish_time": "2024-01-12T10:30:00Z",
      "view_count": 2856,
      "author": {
        "id": 5,
        "wechat_nickname": "电商专家",
        "author_name": "张三"
      }
    },
    {
      "id": 124,
      "title": "AI技术在电商中的应用实践",
      "cover_image": "https://example.com/images/cover124.jpg",
      "publish_time": "2024-01-11T14:20:00Z",
      "view_count": 1923,
      "author": {
        "id": 3,
        "wechat_nickname": "AI研究员",
        "author_name": "李四"
      }
    },
    {
      "id": 125,
      "title": "数字化转型的关键要素",
      "cover_image": "https://example.com/images/cover125.jpg",
      "publish_time": "2024-01-10T09:15:00Z",
      "view_count": 1567,
      "author": {
        "id": 7,
        "wechat_nickname": "科技观察者",
        "author_name": "王五"
      }
    }
  ],
  "message": "获取周榜热门文章成功"
}
```

**错误返回示例**:
```json
{
  "success": false,
  "data": null,
  "message": "无效的榜单类型"
}
```

## 响应格式

所有接口都遵循统一的响应格式：

### 成功响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "total_records": 0
}
```

**字段说明**:
- `success`: 布尔值，表示请求是否成功
- `data`: 具体的返回数据，根据接口不同而变化
- `message`: 操作结果的描述信息
- `total_records`: 总记录数（仅在分页查询时返回）

### 错误响应格式

```json
{
  "success": false,
  "data": null,
  "message": "错误描述信息"
}
```

### 常见HTTP状态码

| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 请求成功 | 正常获取数据 |
| 404 | 资源不存在 | 文章ID不存在或未发布 |
| 422 | 请求参数错误 | 参数类型错误或超出范围 |
| 500 | 服务器内部错误 | 数据库连接失败等 |

## 前端集成示例

### JavaScript/Vue.js 示例

```javascript
// 获取文章列表
async function getArticleList(page = 1, pageSize = 10, filters = {}) {
  // 构建查询参数
  const params = new URLSearchParams({
    page: page.toString(),
    page_size: pageSize.toString()
  });

  // 添加筛选条件
  if (filters.channel) params.append('channel', filters.channel);
  if (filters.tag_id) params.append('tag_id', filters.tag_id.toString());
  if (filters.author_id) params.append('author_id', filters.author_id.toString());
  if (filters.keyword) params.append('keyword', filters.keyword);

  const response = await fetch(`/public-article/list?${params}`);
  const result = await response.json();

  if (result.success) {
    return {
      articles: result.data,
      total: result.total_records,
      currentPage: page,
      pageSize: pageSize,
      totalPages: Math.ceil(result.total_records / pageSize)
    };
  } else {
    throw new Error(result.message);
  }
}

// 获取文章详情
async function getArticleDetail(articleId) {
  if (!articleId || articleId <= 0) {
    throw new Error('无效的文章ID');
  }

  const response = await fetch(`/public-article/detail/${articleId}`);

  if (response.status === 404) {
    throw new Error('文章不存在或未发布');
  }

  const result = await response.json();

  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}

// 获取频道列表
async function getChannels() {
  const response = await fetch('/public-article/channels');
  const result = await response.json();

  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}

// 获取标签列表
async function getTags() {
  const response = await fetch('/public-article/tags');
  const result = await response.json();

  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}

// 获取热门文章
async function getHotArticles(period = 'weekly', limit = 6) {
  if (!['daily', 'weekly', 'monthly'].includes(period)) {
    throw new Error('无效的榜单类型，请使用 daily、weekly 或 monthly');
  }

  const params = new URLSearchParams({
    period: period,
    limit: limit.toString()
  });

  const response = await fetch(`/public-article/hot?${params}`);
  const result = await response.json();

  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}

// 使用示例
try {
  // 获取头条频道的文章，包含关键词搜索
  const articlesResult = await getArticleList(1, 10, {
    channel: '头条',
    keyword: '电商'
  });
  console.log('文章列表:', articlesResult.articles);
  console.log('总页数:', articlesResult.totalPages);

  // 获取文章详情
  const detail = await getArticleDetail(123);
  console.log('文章标题:', detail.title);
  console.log('浏览量:', detail.view_count);
  console.log('作者:', detail.author.author_name);

  // 获取频道列表
  const channels = await getChannels();
  console.log('可用频道:', channels.map(c => c.channel));

  // 获取热门文章
  const hotArticles = await getHotArticles('weekly', 6);
  console.log('周榜热门文章:', hotArticles.articles);
  console.log('榜单时间范围:', hotArticles.time_range);

} catch (error) {
  console.error('请求失败:', error.message);
}
```

### React 示例

```jsx
import { useState, useEffect } from 'react';

function ArticleList() {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    fetchArticles();
  }, [page]);

  const fetchArticles = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/public-article/list?page=${page}&page_size=10`);
      const result = await response.json();
      
      if (result.success) {
        setArticles(result.data);
        setTotal(result.total_records);
      }
    } catch (error) {
      console.error('获取文章失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div>
      {articles.map(article => (
        <div key={article.id} className="article-item">
          <h3>{article.title}</h3>
          <p>{article.summary}</p>
          <div>
            <span>作者: {article.author.author_name}</span>
            <span>发布时间: {new Date(article.publish_time).toLocaleDateString()}</span>
          </div>
          <div>
            {article.tags.map(tag => (
              <span key={tag.id} className="tag">{tag.name}</span>
            ))}
          </div>
        </div>
      ))}
      
      {/* 分页组件 */}
      <div className="pagination">
        <button 
          onClick={() => setPage(p => Math.max(1, p - 1))}
          disabled={page === 1}
        >
          上一页
        </button>
        <span>第 {page} 页</span>
        <button 
          onClick={() => setPage(p => p + 1)}
          disabled={page * 10 >= total}
        >
          下一页
        </button>
      </div>
    </div>
  );
}
```

## 注意事项

1. **文章状态**: 只返回状态为"3"（已发布）且`is_visible=true`的文章
2. **性能考虑**: 建议前端实现分页加载，避免一次性加载过多数据
3. **缓存策略**: 文章列表可以考虑客户端缓存，文章详情建议实时获取
4. **SEO优化**: 文章详情页面可以使用服务端渲染(SSR)提升SEO效果
5. **浏览量统计**: 每次访问文章详情都会自动增加浏览量

## 错误处理

常见错误码：
- `404`: 文章不存在或未发布
- `500`: 服务器内部错误

建议前端做好错误处理和用户提示。
