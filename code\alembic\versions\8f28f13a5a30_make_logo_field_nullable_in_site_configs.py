"""make logo field nullable in site_configs

Revision ID: 8f28f13a5a30
Revises: 742c20a7d627
Create Date: 2025-07-15 09:37:53.292124

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8f28f13a5a30'
down_revision: Union[str, None] = '742c20a7d627'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('site_configs', 'logo',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255),
               nullable=True,
               existing_comment='logo图片路径')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('site_configs', 'logo',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255),
               nullable=False,
               existing_comment='logo图片路径')
    # ### end Alembic commands ###
