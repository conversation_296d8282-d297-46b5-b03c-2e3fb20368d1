from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
import logging
import re

from app.database import database
from app.model import models, schemas
from app.model.dependency import get_current_user
from app.model.utils import HourliveException

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

def get_first_letter(text: str) -> str:
    """获取中文或英文的首字母"""
    if not text:
        return ""
    
    first_char = text[0].upper()
    
    # 如果是英文字母，直接返回
    if re.match(r'[A-Z]', first_char):
        return first_char
    
    # 如果是中文，使用拼音库获取首字母
    try:
        from pypinyin import lazy_pinyin, Style
        pinyin_list = lazy_pinyin(first_char, style=Style.FIRST_LETTER)
        return pinyin_list[0].upper() if pinyin_list else ""
    except ImportError:
        # 如果没有安装pypinyin，返回默认值
        return "Z"

@router.post("/create", response_model=schemas.StandardResponse)
async def create_article_tag(
    tag: schemas.ArticleTagCreate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """创建文章标签"""
    try:
        # 检查标签名是否已存在
        existing_tag = db.query(models.ArticleTag).filter(
            models.ArticleTag.name == tag.name
        ).first()
        
        if existing_tag:
            raise HourliveException(message="标签名已存在")
        
        # 检查分类是否存在
        category = db.query(models.ArticleCategory).filter(
            models.ArticleCategory.id == tag.category_id
        ).first()
        
        if not category:
            raise HourliveException(message="标签分类不存在")
        
        # 自动生成首字母
        first_letter = get_first_letter(tag.name)
        
        db_tag = models.ArticleTag(
            name=tag.name,
            category_id=tag.category_id,
            first_letter=first_letter,
            creator_id=current_user.id,
            updater_id=current_user.id
        )
        
        db.add(db_tag)
        db.commit()
        db.refresh(db_tag)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleTagRead.from_orm(db_tag),
            message="文章标签创建成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建文章标签失败: {str(e)}")
        raise HourliveException(message=f"创建失败: {str(e)}")

@router.get("/list", response_model=schemas.StandardResponse)
async def get_article_tags(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页条数"),
    name: Optional[str] = Query(None, description="标签名模糊查询"),
    first_letter: Optional[str] = Query(None, description="首字母精确查询"),
    category_id: Optional[int] = Query(None, description="标签分类精确查询"),
    category_weight: Optional[str] = Query(None, description="分类权重精确查询"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取文章标签列表"""
    try:
        query = db.query(models.ArticleTag).join(models.ArticleCategory)
        
        # 添加筛选条件
        if name:
            query = query.filter(models.ArticleTag.name.contains(name))
        if first_letter:
            query = query.filter(models.ArticleTag.first_letter == first_letter.upper())
        if category_id:
            query = query.filter(models.ArticleTag.category_id == category_id)
        if category_weight:
            query = query.filter(models.ArticleCategory.weight == category_weight)
        
        # 获取总数
        total_count = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        tags = query.offset(offset).limit(page_size).all()
        
        return schemas.StandardResponse(
            success=True,
            data=[schemas.ArticleTagRead.from_orm(tag) for tag in tags],
            total_records=total_count,
            message="获取文章标签列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取文章标签列表失败: {str(e)}")
        raise HourliveException(message=f"获取失败: {str(e)}")

@router.get("/{tag_id}", response_model=schemas.StandardResponse)
async def get_article_tag(
    tag_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取单个文章标签详情"""
    try:
        tag = db.query(models.ArticleTag).filter(
            models.ArticleTag.id == tag_id
        ).first()
        
        if not tag:
            raise HourliveException(message="文章标签不存在")
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleTagRead.from_orm(tag),
            message="获取文章标签详情成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        logger.error(f"获取文章标签详情失败: {str(e)}")
        raise HourliveException(message=f"获取失败: {str(e)}")

@router.put("/{tag_id}", response_model=schemas.StandardResponse)
async def update_article_tag(
    tag_id: int,
    tag_update: schemas.ArticleTagUpdate,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """更新文章标签"""
    try:
        tag = db.query(models.ArticleTag).filter(
            models.ArticleTag.id == tag_id
        ).first()
        
        if not tag:
            raise HourliveException(message="文章标签不存在")
        
        # 检查标签名是否已存在
        if tag_update.name and tag_update.name != tag.name:
            existing_tag = db.query(models.ArticleTag).filter(
                models.ArticleTag.name == tag_update.name
            ).first()
            if existing_tag:
                raise HourliveException(message="标签名已存在")
        
        # 检查分类是否存在
        if tag_update.category_id and tag_update.category_id != tag.category_id:
            category = db.query(models.ArticleCategory).filter(
                models.ArticleCategory.id == tag_update.category_id
            ).first()
            if not category:
                raise HourliveException(message="标签分类不存在")
        
        # 更新字段
        update_data = tag_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(tag, field, value)
        
        # 如果标签名更新了，重新生成首字母
        if tag_update.name:
            tag.first_letter = get_first_letter(tag_update.name)
        
        tag.updater_id = current_user.id
        
        db.commit()
        db.refresh(tag)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleTagRead.from_orm(tag),
            message="文章标签更新成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新文章标签失败: {str(e)}")
        raise HourliveException(message=f"更新失败: {str(e)}")

@router.delete("/{tag_id}", response_model=schemas.StandardResponse)
async def delete_article_tag(
    tag_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """删除文章标签"""
    try:
        tag = db.query(models.ArticleTag).filter(
            models.ArticleTag.id == tag_id
        ).first()
        
        if not tag:
            raise HourliveException(message="文章标签不存在")
        
        # 检查是否有文章使用此标签
        article_count = db.query(models.Article).join(models.Article.tags).filter(
            models.ArticleTag.id == tag_id
        ).count()
        
        if article_count > 0:
            raise HourliveException(message=f"无法删除标签，还有 {article_count} 篇文章正在使用此标签")
        
        db.delete(tag)
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            message="文章标签删除成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除文章标签失败: {str(e)}")
        raise HourliveException(message=f"删除失败: {str(e)}")