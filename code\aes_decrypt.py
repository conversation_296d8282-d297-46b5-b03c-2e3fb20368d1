import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

def aes_decrypt(encrypted_str, key):
    """
    AES解密（对应Java的AES/ECB/PKCS5Padding）
    
    :param encrypted_str: Base64编码的加密字符串
    :param key: 密钥（AES-128需要16字节，AES-192需要24字节，AES-256需要32字节）
    :return: 解密后的明文
    """
    try:
        # 1. Base64解码
        encrypted_bytes = base64.b64decode(encrypted_str)
        
        # 2. 准备密钥（转为字节流）
        key_bytes = key.encode('utf-8')
        
        # 3. 初始化AES加密器（ECB模式，PKCS5Padding对应Python的PKCS#7）
        cipher = AES.new(key_bytes, AES.MODE_ECB)
        
        # 4. 解密并去除填充
        decrypted_bytes = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)
        
        # 5. 转为字符串
        return decrypted_bytes.decode('utf-8')
    
    except Exception as e:
        raise Exception(f"解密失败: {str(e)}")

# 使用示例
if __name__ == "__main__":
    try:
        # 待解密的Base64字符串
        encrypted_data = "oPC+4SMNp38GVcjJA8A9vEK+oWdGN3T1r1e3n+K+CUnF+YO49LHqs4+GdrKc9Qt4ArKaZ0TxWX5XJewe4r+l3QCihNShCfWRxqzHJXlbeuD9XY2RAAkXYGbxEb7c7XRWnWQkN0kLCn0lb/8ecduVstaRHiUwtcUV1NcreYxGVCBcb3y+UR0E/54PM2HssSDDNbV7O5w3hQ16nERxzVOvd1Znm40sFJ2QKSEosccpcVY="
        
        # 密钥（16字节，对应AES-128）
        secret_key = "1234567890123456"
        
        # 执行解密
        decrypted = aes_decrypt(encrypted_data, secret_key)
        print("解密结果:", decrypted)
        
    except Exception as e:
        print("错误:", e)
    