# 图片管理API路由总览

## 完整的API路由列表

### 🖼️ 图片管理 (`/image`)

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| POST | `/image/upload` | 上传图片 | 支持多种图片格式，可关联标签和分类 |
| GET | `/image/list` | 获取图片列表 | 支持分页、筛选、搜索 |
| GET | `/image/{image_id}` | 获取图片详情 | 包含图片信息和关联的标签 |
| PUT | `/image/{image_id}` | 更新图片信息 | 可更新描述、分类、标签等 |
| DELETE | `/image/{image_id}` | 删除图片 | 同时从OSS和数据库删除 |
| POST | `/image/batch-delete` | 批量删除图片 | 支持一次删除多张图片 |

### 🏷️ 标签管理 (`/image-tag`)

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| POST | `/image-tag/create` | 创建标签 | 创建新的图片标签 |
| GET | `/image-tag/list` | 获取标签列表 | 支持分页和关键词搜索 |
| GET | `/image-tag/detail/{tag_id}` | 获取标签详情 | 包含标签信息和使用统计 |
| PUT | `/image-tag/update/{tag_id}` | 更新标签 | 更新标签名称、描述、颜色等 |
| DELETE | `/image-tag/delete/{tag_id}` | 删除标签 | 支持强制删除选项 |
| GET | `/image-tag/images/{tag_id}` | 获取标签下的图片 | 查看使用指定标签的所有图片 |

### 📁 分类管理 (`/image-category`)

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| GET | `/image-category/list` | 获取分类列表 | 支持统计信息选项 |
| GET | `/image-category/images/{category_name}` | 获取分类下的图片 | 支持分页和筛选 |
| GET | `/image-category/stats/{category_name}` | 获取分类统计 | 详细的分类统计信息 |
| PUT | `/image-category/rename/{old_category_name}` | 重命名分类 | 批量更新分类名称 |
| DELETE | `/image-category/delete/{category_name}` | 删除分类 | 支持清空或删除图片两种模式 |

## API使用示例

### 图片管理示例

```bash
# 上传图片
curl -X POST "http://localhost:9000/image/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@photo.jpg" \
  -F "description=美丽的风景" \
  -F "category=风景" \
  -F "tag_ids=1,2,3"

# 获取图片列表
curl -X GET "http://localhost:9000/image/list?page=1&page_size=20" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取图片详情
curl -X GET "http://localhost:9000/image/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 标签管理示例

```bash
# 创建标签
curl -X POST "http://localhost:9000/image-tag/create" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "自然风光",
    "description": "自然风景图片",
    "color": "#4CAF50"
  }'

# 获取标签列表
curl -X GET "http://localhost:9000/image-tag/list" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取标签详情
curl -X GET "http://localhost:9000/image-tag/detail/1" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取标签下的图片
curl -X GET "http://localhost:9000/image-tag/images/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 分类管理示例

```bash
# 获取分类列表（带统计）
curl -X GET "http://localhost:9000/image-category/list?include_stats=true" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取分类下的图片
curl -X GET "http://localhost:9000/image-category/images/风景" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取分类统计
curl -X GET "http://localhost:9000/image-category/stats/风景" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 重命名分类
curl -X PUT "http://localhost:9000/image-category/rename/风景?new_category_name=自然风光" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 路由特点

### ✅ 优势
1. **清晰的层次结构**: 使用 `image-` 前缀区分不同功能模块
2. **语义化路径**: 路径名称直观反映功能用途
3. **RESTful设计**: 遵循REST API设计原则
4. **一致性**: 与现有文章管理API保持一致的命名规范

### 🔧 路径设计原则
- **动作前缀**: 使用 `create`、`list`、`detail`、`update`、`delete` 等动作词
- **资源标识**: 使用ID或名称标识具体资源
- **嵌套关系**: 通过路径体现资源间的关系

### 📝 注意事项
1. 所有API都需要Bearer Token认证
2. 分类名称在URL中需要进行URL编码
3. 批量操作支持事务回滚
4. 删除操作会同时清理OSS存储

## 测试和文档

- **API文档**: http://localhost:9000/docs
- **测试脚本**: `python test_image_routes.py`
- **文档生成**: `python generate_api_docs.py`
