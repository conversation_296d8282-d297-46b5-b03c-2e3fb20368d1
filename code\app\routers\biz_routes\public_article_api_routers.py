import logging
from typing import Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc

from app.database import database
from app.model import models, schemas

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/list", response_model=schemas.StandardResponse)
async def get_public_article_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=50, description="每页条数"),
    channel: Optional[str] = Query(None, description="发布频道筛选（头条、百科、快讯）"),
    tag_id: Optional[int] = Query(None, description="标签ID筛选"),
    author_id: Optional[int] = Query(None, description="作者ID筛选"),
    keyword: Optional[str] = Query(None, description="关键词搜索（标题、简介）"),
    db: Session = Depends(database.get_db)
):
    """
    获取用户端文章列表
    
    - 不需要登录认证
    - 只返回已发布且可见的文章
    - 按发表时间倒序排序
    - 支持分页和多种筛选条件
    """
    try:
        # 构建查询，只查询已发布且可见的文章
        query = db.query(models.Article).options(
            joinedload(models.Article.author),
            joinedload(models.Article.tags)
        ).filter(
            and_(
                models.Article.status == "3",  # 已发布
                models.Article.is_visible == True  # 可见
            )
        )
        
        # 频道筛选
        if channel:
            query = query.filter(models.Article.channel == channel)
        
        # 标签筛选
        if tag_id:
            query = query.join(models.Article.tags).filter(
                models.ArticleTag.id == tag_id
            )
        
        # 作者筛选
        if author_id:
            query = query.filter(models.Article.author_id == author_id)
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    models.Article.title.contains(keyword),
                    models.Article.summary.contains(keyword)
                )
            )
        
        # 按发表时间倒序排序
        query = query.order_by(desc(models.Article.publish_time))
        
        # 总记录数
        total_records = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        articles = query.offset(offset).limit(page_size).all()
        
        # 转换为用户端格式
        article_list = []
        for article in articles:
            article_data = {
                "id": article.id,
                "title": article.title,
                "cover_image": article.cover_image,
                "summary": article.summary,
                "publish_time": article.publish_time,
                "author": {
                    "id": article.author.id,
                    "wechat_nickname": article.author.wechat_nickname,
                    "author_name": article.author.author_name
                },
                "tags": [
                    {
                        "id": tag.id,
                        "name": tag.name
                    } for tag in article.tags
                ]
            }
            article_list.append(article_data)
        
        return schemas.StandardResponse(
            success=True,
            data=article_list,
            total_records=total_records,
            message="获取文章列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取用户端文章列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文章列表失败: {str(e)}")

@router.get("/detail/{article_id}", response_model=schemas.StandardResponse)
async def get_public_article_detail(
    article_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(database.get_db)
):
    """
    获取用户端文章详情
    
    - 不需要登录认证
    - 只能查看已发布且可见的文章
    - 自动增加浏览量
    - 返回完整的文章内容
    """
    try:
        # 查询文章，包含关联数据
        article = db.query(models.Article).options(
            joinedload(models.Article.author),
            joinedload(models.Article.tags)
        ).filter(
            and_(
                models.Article.id == article_id,
                models.Article.status == "3",  # 已发布
                models.Article.is_visible == True  # 可见
            )
        ).first()
        
        if not article:
            raise HTTPException(status_code=404, detail="文章不存在或未发布")
        
        # 增加浏览量
        article.view_count += 1
        db.commit()
        
        # 构建返回数据
        article_detail = {
            "id": article.id,
            "title": article.title,
            "cover_image": article.cover_image,
            "summary": article.summary,
            "content": article.content,
            "style": article.style,
            "publish_time": article.publish_time,
            "view_count": article.view_count,
            "like_count": article.like_count,
            "favorite_count": article.favorite_count,
            "comment_count": article.comment_count,
            "author": {
                "id": article.author.id,
                "wechat_nickname": article.author.wechat_nickname,
                "author_name": article.author.author_name
            },
            "tags": [
                {
                    "id": tag.id,
                    "name": tag.name
                } for tag in article.tags
            ]
        }
        
        return schemas.StandardResponse(
            success=True,
            data=article_detail,
            message="获取文章详情成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户端文章详情失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"获取文章详情失败: {str(e)}")

@router.get("/channels", response_model=schemas.StandardResponse)
async def get_article_channels(
    db: Session = Depends(database.get_db)
):
    """
    获取文章频道列表
    
    - 不需要登录认证
    - 返回所有有文章的频道及其文章数量
    """
    try:
        from sqlalchemy import func, distinct
        
        # 查询所有已发布文章的频道及数量
        channels_with_count = db.query(
            models.Article.channel,
            func.count(models.Article.id).label('article_count')
        ).filter(
            and_(
                models.Article.status == "3",  # 已发布
                models.Article.is_visible == True  # 可见
            )
        ).group_by(models.Article.channel).all()
        
        channel_list = [
            {
                "channel": channel.channel,
                "article_count": channel.article_count
            }
            for channel in channels_with_count
        ]
        
        return schemas.StandardResponse(
            success=True,
            data=channel_list,
            message="获取频道列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取文章频道列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取频道列表失败: {str(e)}")

@router.get("/tags", response_model=schemas.StandardResponse)
async def get_article_tags(
    db: Session = Depends(database.get_db)
):
    """
    获取文章标签列表
    
    - 不需要登录认证
    - 返回所有有文章关联的标签
    """
    try:
        from sqlalchemy import func
        
        # 查询所有有文章关联的标签
        tags_with_count = db.query(
            models.ArticleTag,
            func.count(models.Article.id).label('article_count')
        ).join(
            models.Article.tags
        ).filter(
            and_(
                models.Article.status == "3",  # 已发布
                models.Article.is_visible == True  # 可见
            )
        ).group_by(models.ArticleTag.id).all()
        
        tag_list = []
        for tag_info in tags_with_count:
            tag_data = schemas.ArticleTagRead.from_orm(tag_info[0]).dict()
            tag_data['article_count'] = tag_info[1]
            tag_list.append(tag_data)
        
        return schemas.StandardResponse(
            success=True,
            data=tag_list,
            message="获取标签列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取文章标签列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取标签列表失败: {str(e)}")

@router.get("/authors", response_model=schemas.StandardResponse)
async def get_article_authors(
    db: Session = Depends(database.get_db)
):
    """
    获取文章作者列表
    
    - 不需要登录认证
    - 返回所有有文章的作者及其文章数量
    """
    try:
        from sqlalchemy import func
        
        # 查询所有有文章的作者
        authors_with_count = db.query(
            models.ArticleAuthor,
            func.count(models.Article.id).label('article_count')
        ).join(
            models.Article, models.ArticleAuthor.id == models.Article.author_id
        ).filter(
            and_(
                models.Article.status == "3",  # 已发布
                models.Article.is_visible == True  # 可见
            )
        ).group_by(models.ArticleAuthor.id).all()
        
        author_list = []
        for author_info in authors_with_count:
            author_data = schemas.ArticleAuthorRead.from_orm(author_info[0]).dict()
            author_data['article_count'] = author_info[1]
            author_list.append(author_data)
        
        return schemas.StandardResponse(
            success=True,
            data=author_list,
            message="获取作者列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取文章作者列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取作者列表失败: {str(e)}")

@router.get("/hot", response_model=schemas.StandardResponse)
async def get_hot_articles(
    period: str = Query(..., regex="^(daily|weekly|monthly)$", description="榜单类型：daily-日榜，weekly-周榜，monthly-月榜"),
    limit: int = Query(6, ge=1, le=20, description="返回文章数量，默认6篇，最多20篇"),
    db: Session = Depends(database.get_db)
):
    """
    获取热门文章榜单

    - 不需要登录认证
    - 根据时间段和阅读量排序
    - 只返回已发布且可见的文章
    - 返回简化的文章信息（封面图、标题、发表时间）
    """
    try:
        # 计算时间范围
        now = datetime.now()
        if period == "daily":
            start_time = now - timedelta(days=1)
            period_name = "日榜"
        elif period == "weekly":
            start_time = now - timedelta(weeks=1)
            period_name = "周榜"
        elif period == "monthly":
            start_time = now - timedelta(days=30)
            period_name = "月榜"
        else:
            raise HTTPException(status_code=400, detail="无效的榜单类型")

        # 构建查询，只查询已发布且可见的文章
        query = db.query(models.Article).options(
            joinedload(models.Article.author)
        ).filter(
            and_(
                models.Article.status == "3",  # 已发布
                models.Article.is_visible == True,  # 可见
                models.Article.publish_time >= start_time,  # 在指定时间范围内
                models.Article.publish_time <= now
            )
        )

        # 按阅读量降序排序，获取前N篇
        hot_articles = query.order_by(desc(models.Article.view_count)).limit(limit).all()

        # 转换为简化格式（只返回必要字段）
        article_list = []
        for article in hot_articles:
            article_data = {
                "id": article.id,
                "title": article.title,
                "cover_image": article.cover_image,
                "publish_time": article.publish_time,
                "view_count": article.view_count,
                "author": {
                    "id": article.author.id,
                    "wechat_nickname": article.author.wechat_nickname,
                    "author_name": article.author.author_name
                }
            }
            article_list.append(article_data)

        return schemas.StandardResponse(
            success=True,
            data=article_list,
            message=f"获取{period_name}热门文章成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取热门文章失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取热门文章失败: {str(e)}")
