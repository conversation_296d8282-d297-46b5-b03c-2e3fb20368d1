"""add article management tables

Revision ID: 946f2be3582d
Revises: 8f28f13a5a30
Create Date: 2025-07-16 11:00:49.676046

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '946f2be3582d'
down_revision: Union[str, None] = '8f28f13a5a30'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('article_authors',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.Column('wechat_nickname', sa.String(length=30), nullable=False, comment='微信昵称'),
    sa.<PERSON>umn('author_name', sa.String(length=30), nullable=False, comment='作者名'),
    sa.Column('creator_id', sa.Integer(), nullable=False),
    sa.Column('updater_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['creator_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['updater_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_article_authors_id'), 'article_authors', ['id'], unique=False)
    op.create_table('article_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.Column('name', sa.String(length=20), nullable=False, comment='分类名称'),
    sa.Column('weight', sa.String(length=1), nullable=False, comment='分类权重：1.合作伙伴 2.文章性质 3.服务品类 4.国家/地区 5.平台/独立站'),
    sa.Column('seo_title', sa.String(length=100), nullable=True, comment='SEO标题'),
    sa.Column('seo_keywords', sa.String(length=100), nullable=True, comment='SEO关键字'),
    sa.Column('seo_description', sa.String(length=300), nullable=True, comment='SEO描述'),
    sa.Column('creator_id', sa.Integer(), nullable=False),
    sa.Column('updater_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['creator_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['updater_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_article_categories_id'), 'article_categories', ['id'], unique=False)
    op.create_table('article_tags',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.Column('name', sa.String(length=20), nullable=False, comment='标签名'),
    sa.Column('first_letter', sa.String(length=1), nullable=False, comment='首字母'),
    sa.Column('article_count', sa.Integer(), nullable=True, comment='关联文章数'),
    sa.Column('article_read_count', sa.Integer(), nullable=True, comment='关联文章阅读数'),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('creator_id', sa.Integer(), nullable=False),
    sa.Column('updater_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['article_categories.id'], ),
    sa.ForeignKeyConstraint(['creator_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['updater_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_article_tags_id'), 'article_tags', ['id'], unique=False)
    op.create_index(op.f('ix_article_tags_name'), 'article_tags', ['name'], unique=True)
    op.create_table('articles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.Column('title', sa.String(length=100), nullable=False, comment='标题'),
    sa.Column('style', sa.String(length=20), nullable=False, comment='风格：富文本、markdown'),
    sa.Column('content', sa.Text(), nullable=False, comment='文章内容'),
    sa.Column('channel', sa.String(length=20), nullable=False, comment='发布频道：头条、百科、快讯'),
    sa.Column('cover_image', sa.String(length=500), nullable=True, comment='文章封面'),
    sa.Column('summary', sa.String(length=300), nullable=True, comment='简介'),
    sa.Column('share_image', sa.String(length=500), nullable=True, comment='分享封面'),
    sa.Column('publish_time', sa.DateTime(), nullable=False, comment='发表时间'),
    sa.Column('is_visible', sa.Boolean(), nullable=True, comment='是否展示'),
    sa.Column('seo_title', sa.String(length=100), nullable=True, comment='SEO标题'),
    sa.Column('seo_keywords', sa.String(length=100), nullable=True, comment='SEO关键字'),
    sa.Column('seo_description', sa.String(length=300), nullable=True, comment='SEO描述'),
    sa.Column('view_count', sa.Integer(), nullable=True, comment='浏览数'),
    sa.Column('like_count', sa.Integer(), nullable=True, comment='点赞数'),
    sa.Column('favorite_count', sa.Integer(), nullable=True, comment='收藏数'),
    sa.Column('comment_count', sa.Integer(), nullable=True, comment='评论数'),
    sa.Column('status', sa.String(length=1), nullable=False, comment='状态：0.草稿 1.待审核 2.审核中 3.已发布'),
    sa.Column('author_id', sa.Integer(), nullable=False),
    sa.Column('creator_id', sa.Integer(), nullable=False),
    sa.Column('updater_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['author_id'], ['article_authors.id'], ),
    sa.ForeignKeyConstraint(['creator_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['updater_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_articles_id'), 'articles', ['id'], unique=False)
    op.create_table('article_tag_association',
    sa.Column('article_id', sa.Integer(), nullable=False),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['article_id'], ['articles.id'], ),
    sa.ForeignKeyConstraint(['tag_id'], ['article_tags.id'], ),
    sa.PrimaryKeyConstraint('article_id', 'tag_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('article_tag_association')
    op.drop_index(op.f('ix_articles_id'), table_name='articles')
    op.drop_table('articles')
    op.drop_index(op.f('ix_article_tags_name'), table_name='article_tags')
    op.drop_index(op.f('ix_article_tags_id'), table_name='article_tags')
    op.drop_table('article_tags')
    op.drop_index(op.f('ix_article_categories_id'), table_name='article_categories')
    op.drop_table('article_categories')
    op.drop_index(op.f('ix_article_authors_id'), table_name='article_authors')
    op.drop_table('article_authors')
    # ### end Alembic commands ###
