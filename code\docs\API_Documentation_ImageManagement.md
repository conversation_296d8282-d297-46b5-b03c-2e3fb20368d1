# 图片资源管理 API 文档

## 概述

该API系统提供了完整的图片资源管理功能，包括图片上传、存储、标签管理、搜索筛选等功能。系统基于FastAPI开发，使用阿里云OSS作为图片存储，MySQL作为元数据存储。

## 认证方式

所有API都需要JWT Bearer Token认证。在请求头中添加：
```
Authorization: Bearer <your_jwt_token>
```

## 数据库设计

### 图片表 (images)
- `id`: 图片ID（主键）
- `original_name`: 图片原始名称
- `new_name`: 图片新名称（存储名，UUID生成）
- `access_url`: 图片访问URL
- `file_size`: 文件大小（字节）
- `file_type`: 文件类型（jpg, png等）
- `width`: 图片宽度
- `height`: 图片高度
- `description`: 图片描述
- `is_public`: 是否公开访问
- `category`: 图片分类
- `uploader_id`: 上传者ID（外键关联user表）
- `create_time`: 创建时间
- `update_time`: 更新时间

### 标签表 (tags)
- `id`: 标签ID（主键）
- `name`: 标签名称（唯一）
- `description`: 标签描述
- `creator_id`: 创建者ID（外键关联user表）
- `create_time`: 创建时间
- `update_time`: 更新时间

### 图片标签关联表 (image_tag_association)
- `image_id`: 图片ID（外键）
- `tag_id`: 标签ID（外键）

## API接口列表

### 图片管理接口

#### 1. 上传图片
**POST** `/image/upload`

**请求方式**: multipart/form-data

**参数**:
- `file`: 图片文件（必填）
- `original_name`: 图片原始名称（可选，默认使用上传文件名）
- `description`: 图片描述（可选）
- `category`: 图片分类（可选）
- `is_public`: 是否公开（可选，默认true）
- `tag_ids`: 标签ID列表，逗号分隔（可选，如"1,2,3"）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "original_name": "example.jpg",
    "new_name": "550e8400-e29b-41d4-a716-446655440000.jpg",
    "access_url": "https://script.hourliveapi.com/images/550e8400-e29b-41d4-a716-446655440000.jpg",
    "file_size": 102400,
    "file_type": "jpg",
    "width": 1920,
    "height": 1080,
    "description": "示例图片",
    "is_public": true,
    "category": "banner",
    "uploader_id": 1,
    "tags": [
      {
        "id": 1,
        "name": "广告",
        "description": "广告类图片",
        "creator_id": 1,
        "create_time": "2024-01-01T00:00:00",
        "update_time": "2024-01-01T00:00:00"
      }
    ],
    "create_time": "2024-01-01T00:00:00",
    "update_time": "2024-01-01T00:00:00"
  },
  "message": "图片上传成功"
}
```

#### 2. 获取图片列表
**GET** `/image/list`

**查询参数**:
- `tag_ids`: 标签ID列表，逗号分隔（可选）
- `category`: 分类筛选（可选）
- `keyword`: 关键词搜索（可选，搜索名称或描述）
- `is_public`: 是否公开（可选）
- `uploader_id`: 上传者ID（可选）
- `page`: 页码（默认1）
- `page_size`: 每页大小（默认20，最大100）

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "original_name": "example.jpg",
      "new_name": "550e8400-e29b-41d4-a716-446655440000.jpg",
      "access_url": "https://script.hourliveapi.com/images/550e8400-e29b-41d4-a716-446655440000.jpg",
      "file_size": 102400,
      "file_type": "jpg",
      "width": 1920,
      "height": 1080,
      "description": "示例图片",
      "is_public": true,
      "category": "banner",
      "uploader_id": 1,
      "tags": [],
      "create_time": "2024-01-01T00:00:00",
      "update_time": "2024-01-01T00:00:00"
    }
  ],
  "total_records": 1,
  "message": "获取图片列表成功"
}
```

#### 3. 获取图片详情
**GET** `/image/{image_id}`

**路径参数**:
- `image_id`: 图片ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "original_name": "example.jpg",
    "new_name": "550e8400-e29b-41d4-a716-446655440000.jpg",
    "access_url": "https://script.hourliveapi.com/images/550e8400-e29b-41d4-a716-446655440000.jpg",
    "file_size": 102400,
    "file_type": "jpg",
    "width": 1920,
    "height": 1080,
    "description": "示例图片",
    "is_public": true,
    "category": "banner",
    "uploader_id": 1,
    "tags": [],
    "create_time": "2024-01-01T00:00:00",
    "update_time": "2024-01-01T00:00:00"
  },
  "message": "获取图片详情成功"
}
```

#### 4. 更新图片信息
**PUT** `/image/{image_id}`

**路径参数**:
- `image_id`: 图片ID

**请求体**:
```json
{
  "original_name": "new_name.jpg",
  "description": "更新后的描述",
  "is_public": false,
  "category": "product",
  "tag_ids": [1, 2, 3]
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "original_name": "new_name.jpg",
    "new_name": "550e8400-e29b-41d4-a716-446655440000.jpg",
    "access_url": "https://script.hourliveapi.com/images/550e8400-e29b-41d4-a716-446655440000.jpg",
    "file_size": 102400,
    "file_type": "jpg",
    "width": 1920,
    "height": 1080,
    "description": "更新后的描述",
    "is_public": false,
    "category": "product",
    "uploader_id": 1,
    "tags": [
      {
        "id": 1,
        "name": "广告",
        "description": "广告类图片",
        "creator_id": 1,
        "create_time": "2024-01-01T00:00:00",
        "update_time": "2024-01-01T00:00:00"
      }
    ],
    "create_time": "2024-01-01T00:00:00",
    "update_time": "2024-01-01T00:01:00"
  },
  "message": "图片信息更新成功"
}
```

#### 5. 删除图片
**DELETE** `/image/{image_id}`

**路径参数**:
- `image_id`: 图片ID

**响应示例**:
```json
{
  "success": true,
  "message": "图片删除成功"
}
```

#### 6. 批量删除图片
**POST** `/image/batch-delete`

**请求体**:
```json
[1, 2, 3, 4, 5]
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "deleted_count": 5
  },
  "message": "成功删除 5 张图片"
}
```

### 标签管理接口

#### 7. 创建标签
**POST** `/image/tags`

**请求体**:
```json
{
  "name": "广告",
  "description": "广告类图片标签"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "广告",
    "description": "广告类图片标签",
    "creator_id": 1,
    "create_time": "2024-01-01T00:00:00",
    "update_time": "2024-01-01T00:00:00"
  },
  "message": "标签创建成功"
}
```

#### 8. 获取标签列表
**GET** `/image/tags`

**查询参数**:
- `keyword`: 关键词搜索（可选）
- `page`: 页码（默认1）
- `page_size`: 每页大小（默认50，最大100）

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "广告",
      "description": "广告类图片标签",
      "creator_id": 1,
      "create_time": "2024-01-01T00:00:00",
      "update_time": "2024-01-01T00:00:00"
    }
  ],
  "total_records": 1,
  "message": "获取标签列表成功"
}
```

#### 9. 更新标签
**PUT** `/image/tags/{tag_id}`

**路径参数**:
- `tag_id`: 标签ID

**请求体**:
```json
{
  "name": "更新后的标签名",
  "description": "更新后的描述"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "更新后的标签名",
    "description": "更新后的描述",
    "creator_id": 1,
    "create_time": "2024-01-01T00:00:00",
    "update_time": "2024-01-01T00:01:00"
  },
  "message": "标签更新成功"
}
```

#### 10. 删除标签
**DELETE** `/image/tags/{tag_id}`

**路径参数**:
- `tag_id`: 标签ID

**响应示例**:
```json
{
  "success": true,
  "message": "标签删除成功"
}
```

**注意**: 如果有图片正在使用该标签，将无法删除，返回错误信息。

#### 11. 获取分类列表
**GET** `/image/categories`

**响应示例**:
```json
{
  "success": true,
  "data": ["banner", "product", "avatar", "icon"],
  "message": "获取分类列表成功"
}
```

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `401`: 未授权访问
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述信息",
  "status_code": 400
}
```

## 使用场景示例

### 1. 图片上传流程

```bash
# 上传图片并添加标签
curl -X POST "http://localhost:9000/image/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/image.jpg" \
  -F "description=产品图片" \
  -F "category=product" \
  -F "tag_ids=1,2"
```

### 2. 按标签筛选图片

```bash
# 获取包含标签ID为1和2的图片
curl -X GET "http://localhost:9000/image/list?tag_ids=1,2&page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 关键词搜索

```bash
# 搜索包含"产品"关键词的图片
curl -X GET "http://localhost:9000/image/list?keyword=产品&page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 创建和管理标签

```bash
# 创建新标签
curl -X POST "http://localhost:9000/image/tags" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "营销活动", "description": "营销活动相关图片"}'

# 获取所有标签
curl -X GET "http://localhost:9000/image/tags" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 前端集成指南

### JavaScript 示例

```javascript
class ImageAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  // 上传图片
  async uploadImage(file, options = {}) {
    const formData = new FormData();
    formData.append('file', file);
    
    if (options.description) formData.append('description', options.description);
    if (options.category) formData.append('category', options.category);
    if (options.tagIds) formData.append('tag_ids', options.tagIds.join(','));
    if (options.isPublic !== undefined) formData.append('is_public', options.isPublic);

    const response = await fetch(`${this.baseURL}/image/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
      },
      body: formData
    });

    return response.json();
  }

  // 获取图片列表
  async getImageList(filters = {}) {
    const params = new URLSearchParams();
    if (filters.tagIds) params.append('tag_ids', filters.tagIds.join(','));
    if (filters.category) params.append('category', filters.category);
    if (filters.keyword) params.append('keyword', filters.keyword);
    if (filters.isPublic !== undefined) params.append('is_public', filters.isPublic);
    if (filters.page) params.append('page', filters.page);
    if (filters.pageSize) params.append('page_size', filters.pageSize);

    const response = await fetch(`${this.baseURL}/image/list?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });

    return response.json();
  }

  // 创建标签
  async createTag(tagData) {
    const response = await fetch(`${this.baseURL}/image/tags`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(tagData)
    });

    return response.json();
  }

  // 获取标签列表
  async getTagList() {
    const response = await fetch(`${this.baseURL}/image/tags`, {
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });

    return response.json();
  }
}

// 使用示例
const imageAPI = new ImageAPI('http://localhost:9000', 'your_jwt_token');

// 上传图片
const fileInput = document.getElementById('fileInput');
fileInput.addEventListener('change', async (e) => {
  const file = e.target.files[0];
  if (file) {
    const result = await imageAPI.uploadImage(file, {
      description: '产品图片',
      category: 'product',
      tagIds: [1, 2]
    });
    console.log(result);
  }
});
```

### React Hook 示例

```jsx
import { useState, useEffect } from 'react';

const useImageManagement = (token) => {
  const [images, setImages] = useState([]);
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(false);

  const imageAPI = new ImageAPI('http://localhost:9000', token);

  const uploadImage = async (file, options) => {
    setLoading(true);
    try {
      const result = await imageAPI.uploadImage(file, options);
      if (result.success) {
        setImages(prev => [result.data, ...prev]);
      }
      return result;
    } finally {
      setLoading(false);
    }
  };

  const loadImages = async (filters) => {
    setLoading(true);
    try {
      const result = await imageAPI.getImageList(filters);
      if (result.success) {
        setImages(result.data);
      }
      return result;
    } finally {
      setLoading(false);
    }
  };

  const loadTags = async () => {
    try {
      const result = await imageAPI.getTagList();
      if (result.success) {
        setTags(result.data);
      }
      return result;
    } catch (error) {
      console.error('加载标签失败:', error);
    }
  };

  useEffect(() => {
    loadTags();
  }, []);

  return {
    images,
    tags,
    loading,
    uploadImage,
    loadImages,
    loadTags
  };
};

// 组件中使用
const ImageManagementComponent = () => {
  const { images, tags, loading, uploadImage, loadImages } = useImageManagement('your_jwt_token');

  const handleFileUpload = async (file) => {
    const result = await uploadImage(file, {
      description: '新上传的图片',
      category: 'general'
    });
    
    if (result.success) {
      alert('图片上传成功！');
    } else {
      alert('上传失败：' + result.message);
    }
  };

  return (
    <div>
      <input 
        type="file" 
        accept="image/*"
        onChange={(e) => handleFileUpload(e.target.files[0])}
      />
      
      {loading && <div>加载中...</div>}
      
      <div className="image-grid">
        {images.map(image => (
          <div key={image.id} className="image-item">
            <img src={image.access_url} alt={image.original_name} />
            <div className="image-info">
              <h4>{image.original_name}</h4>
              <p>{image.description}</p>
              <div className="tags">
                {image.tags.map(tag => (
                  <span key={tag.id} className="tag">{tag.name}</span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 性能优化建议

1. **图片压缩**: 上传前建议对图片进行适当压缩
2. **缓存策略**: 利用OSS的CDN功能进行图片缓存
3. **分页加载**: 使用分页参数避免一次性加载过多数据
4. **懒加载**: 前端实现图片懒加载，提升页面性能
5. **图片预览**: 可以使用OSS的图片处理功能生成缩略图

## 安全考虑

1. **文件类型检查**: 系统会验证上传文件的MIME类型
2. **访问控制**: 通过JWT Token控制API访问权限
3. **文件大小限制**: 建议在前端和后端都设置文件大小限制
4. **防盗链**: 可配置OSS防盗链保护图片资源

## 扩展功能

1. **图片编辑**: 可集成图片编辑功能（裁剪、旋转、滤镜等）
2. **批量操作**: 支持批量标签管理、批量分类等
3. **图片搜索**: 可扩展基于AI的图片内容识别和搜索
4. **版本控制**: 支持图片的版本管理和历史记录
5. **统计分析**: 添加图片使用统计和分析功能 