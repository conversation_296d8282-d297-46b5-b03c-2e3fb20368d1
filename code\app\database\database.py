from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.model import models
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from passlib.context import CryptContext
from datetime import datetime, timed<PERSON>ta
from typing import Optional
import os

username = "dny123"
password = "Dny_123com"
host = "rm-wz9kc1ko7s93t1exclo.mysql.rds.aliyuncs.com"
port = "3306"
database = "dny123"

didCreateSessionLocal = False
engine = None
SessionLocal = None

# 依赖注入的方式获取数据库会话
def get_db():
    global didCreateSessionLocal, engine, SessionLocal
    if not didCreateSessionLocal:
        didCreateSessionLocal = True
        engine = create_engine(
            f'mysql+pymysql://{username}:{password}@{host}:{port}/{database}',
            echo=True,
            connect_args={
                "charset": "utf8mb4",
                "collation": "utf8mb4_unicode_ci"
            }
        )
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        models.Base.metadata.create_all(bind=engine)
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
