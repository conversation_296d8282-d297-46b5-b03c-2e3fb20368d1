"""create_image_and_tag_tables

Revision ID: ca6ee6059cb1
Revises: 33c641859593
Create Date: 2025-07-03 14:13:46.341575

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ca6ee6059cb1'
down_revision: Union[str, None] = '33c641859593'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('images',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.Column('original_name', sa.String(length=255), nullable=False, comment='图片原始名称'),
    sa.Column('new_name', sa.String(length=255), nullable=False, comment='图片新名称（存储名）'),
    sa.Column('access_url', sa.String(length=500), nullable=False, comment='图片访问URL'),
    sa.Column('file_size', sa.Integer(), nullable=True, comment='文件大小（字节）'),
    sa.Column('file_type', sa.String(length=50), nullable=True, comment='文件类型（如jpg, png等）'),
    sa.Column('width', sa.Integer(), nullable=True, comment='图片宽度'),
    sa.Column('height', sa.Integer(), nullable=True, comment='图片高度'),
    sa.Column('description', sa.Text(), nullable=True, comment='图片描述'),
    sa.Column('is_public', sa.Boolean(), nullable=True, comment='是否公开访问'),
    sa.Column('category', sa.String(length=50), nullable=True, comment='图片分类'),
    sa.Column('uploader_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['uploader_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_images_id'), 'images', ['id'], unique=False)
    op.create_index(op.f('ix_images_new_name'), 'images', ['new_name'], unique=True)
    op.create_table('tags',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.Column('name', sa.String(length=50), nullable=False, comment='标签名称'),
    sa.Column('description', sa.String(length=255), nullable=True, comment='标签描述'),
    sa.Column('creator_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['creator_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tags_id'), 'tags', ['id'], unique=False)
    op.create_index(op.f('ix_tags_name'), 'tags', ['name'], unique=True)
    op.create_table('image_tag_association',
    sa.Column('image_id', sa.Integer(), nullable=False),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['image_id'], ['images.id'], ),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ),
    sa.PrimaryKeyConstraint('image_id', 'tag_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('image_tag_association')
    op.drop_index(op.f('ix_tags_name'), table_name='tags')
    op.drop_index(op.f('ix_tags_id'), table_name='tags')
    op.drop_table('tags')
    op.drop_index(op.f('ix_images_new_name'), table_name='images')
    op.drop_index(op.f('ix_images_id'), table_name='images')
    op.drop_table('images')
    # ### end Alembic commands ###
