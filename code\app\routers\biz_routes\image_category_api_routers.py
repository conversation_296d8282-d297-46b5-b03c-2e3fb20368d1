import logging
from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, distinct

from app.database import database
from app.model import models, schemas
from app.model.dependency import get_current_user

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/list", response_model=schemas.StandardResponse)
async def get_categories(
    include_stats: bool = Query(False, description="是否包含统计信息"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取所有图片分类列表
    
    - **include_stats**: 是否包含每个分类的图片数量统计
    """
    try:
        if include_stats:
            # 查询分类及其图片数量
            categories_with_count = db.query(
                models.Image.category,
                func.count(models.Image.id).label('image_count')
            ).filter(
                models.Image.category.isnot(None)
            ).group_by(models.Image.category).all()
            
            category_list = [
                {
                    "name": cat.category,
                    "image_count": cat.image_count
                }
                for cat in categories_with_count if cat.category
            ]
            
            # 按图片数量降序排列
            category_list.sort(key=lambda x: x['image_count'], reverse=True)
        else:
            # 只查询分类名称
            categories = db.query(distinct(models.Image.category)).filter(
                models.Image.category.isnot(None)
            ).all()
            
            category_list = [cat[0] for cat in categories if cat[0]]
            category_list.sort()  # 按名称排序
        
        return schemas.StandardResponse(
            success=True,
            data=category_list,
            message="获取分类列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取分类列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

@router.get("/images/{category_name}", response_model=schemas.StandardResponse)
async def get_category_images(
    category_name: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    is_public: Optional[bool] = Query(None, description="是否公开"),
    uploader_id: Optional[int] = Query(None, description="上传者ID"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取指定分类下的图片列表
    
    支持分页和多种筛选条件
    """
    try:
        query = db.query(models.Image).filter(models.Image.category == category_name)
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                models.Image.original_name.contains(keyword) |
                models.Image.description.contains(keyword)
            )
        
        # 公开性筛选
        if is_public is not None:
            query = query.filter(models.Image.is_public == is_public)
        
        # 上传者筛选
        if uploader_id:
            query = query.filter(models.Image.uploader_id == uploader_id)
        
        # 按创建时间降序排列
        query = query.order_by(models.Image.create_time.desc())
        
        # 总记录数
        total_records = query.count()
        
        # 分页
        offset = (page - 1) * page_size
        images = query.offset(offset).limit(page_size).all()
        
        return schemas.StandardResponse(
            success=True,
            data=[schemas.ImageRead.from_orm(image) for image in images],
            total_records=total_records,
            message=f"获取分类 '{category_name}' 的图片列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取分类图片列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")

@router.get("/stats/{category_name}", response_model=schemas.StandardResponse)
async def get_category_stats(
    category_name: str,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    获取指定分类的统计信息
    
    包括图片总数、文件大小统计、上传者统计等
    """
    try:
        # 基础统计
        base_query = db.query(models.Image).filter(models.Image.category == category_name)
        
        total_images = base_query.count()
        if total_images == 0:
            return schemas.StandardResponse(
                success=True,
                data={
                    "category_name": category_name,
                    "total_images": 0,
                    "message": "该分类下暂无图片"
                },
                message="获取分类统计成功"
            )
        
        # 文件大小统计
        size_stats = db.query(
            func.sum(models.Image.file_size).label('total_size'),
            func.avg(models.Image.file_size).label('avg_size'),
            func.max(models.Image.file_size).label('max_size'),
            func.min(models.Image.file_size).label('min_size')
        ).filter(models.Image.category == category_name).first()
        
        # 公开/私有统计
        public_count = base_query.filter(models.Image.is_public == True).count()
        private_count = total_images - public_count
        
        # 上传者统计
        uploader_stats = db.query(
            models.User.username,
            func.count(models.Image.id).label('upload_count')
        ).join(models.Image, models.User.id == models.Image.uploader_id).filter(
            models.Image.category == category_name
        ).group_by(models.User.id, models.User.username).order_by(
            func.count(models.Image.id).desc()
        ).limit(10).all()
        
        # 文件类型统计
        type_stats = db.query(
            models.Image.file_type,
            func.count(models.Image.id).label('count')
        ).filter(models.Image.category == category_name).group_by(
            models.Image.file_type
        ).order_by(func.count(models.Image.id).desc()).all()
        
        stats_data = {
            "category_name": category_name,
            "total_images": total_images,
            "size_stats": {
                "total_size": int(size_stats.total_size or 0),
                "avg_size": int(size_stats.avg_size or 0),
                "max_size": int(size_stats.max_size or 0),
                "min_size": int(size_stats.min_size or 0),
                "total_size_mb": round((size_stats.total_size or 0) / 1024 / 1024, 2)
            },
            "visibility_stats": {
                "public_count": public_count,
                "private_count": private_count
            },
            "top_uploaders": [
                {"username": stat.username, "upload_count": stat.upload_count}
                for stat in uploader_stats
            ],
            "file_type_stats": [
                {"file_type": stat.file_type, "count": stat.count}
                for stat in type_stats
            ]
        }
        
        return schemas.StandardResponse(
            success=True,
            data=stats_data,
            message="获取分类统计成功"
        )
        
    except Exception as e:
        logger.error(f"获取分类统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@router.put("/rename/{old_category_name}", response_model=schemas.StandardResponse)
async def rename_category(
    old_category_name: str,
    new_category_name: str = Query(..., description="新的分类名称"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    重命名分类
    
    将所有属于旧分类的图片更新为新分类名称
    """
    try:
        # 检查旧分类是否存在
        old_category_exists = db.query(models.Image).filter(
            models.Image.category == old_category_name
        ).first()
        
        if not old_category_exists:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        # 检查新分类名是否已存在
        new_category_exists = db.query(models.Image).filter(
            models.Image.category == new_category_name
        ).first()
        
        if new_category_exists:
            raise HTTPException(status_code=400, detail="新分类名已存在")
        
        # 更新所有图片的分类
        updated_count = db.query(models.Image).filter(
            models.Image.category == old_category_name
        ).update({"category": new_category_name})
        
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            data={
                "old_category_name": old_category_name,
                "new_category_name": new_category_name,
                "updated_images_count": updated_count
            },
            message=f"分类重命名成功，已更新 {updated_count} 张图片"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重命名分类失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"重命名失败: {str(e)}")

@router.delete("/delete/{category_name}", response_model=schemas.StandardResponse)
async def delete_category(
    category_name: str,
    action: str = Query("clear", regex="^(clear|delete)$", description="操作类型：clear-清空分类字段，delete-删除图片"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    删除分类
    
    - **action=clear**: 将该分类下所有图片的分类字段设为空（图片保留）
    - **action=delete**: 删除该分类下的所有图片（危险操作）
    """
    try:
        # 检查分类是否存在
        images_in_category = db.query(models.Image).filter(
            models.Image.category == category_name
        ).all()
        
        if not images_in_category:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        if action == "clear":
            # 清空分类字段
            updated_count = db.query(models.Image).filter(
                models.Image.category == category_name
            ).update({"category": None})
            
            db.commit()
            
            return schemas.StandardResponse(
                success=True,
                data={
                    "category_name": category_name,
                    "action": "clear",
                    "affected_images_count": updated_count
                },
                message=f"分类已清空，{updated_count} 张图片的分类字段已清除"
            )
            
        elif action == "delete":
            # 删除所有图片（危险操作）
            from app.oss import oss
            
            deleted_count = 0
            for image in images_in_category:
                try:
                    # 从OSS删除文件
                    oss.delete_image_from_images_directory(image.new_name)
                except Exception as e:
                    logger.warning(f"从OSS删除文件失败: {str(e)}")
                
                # 从数据库删除
                db.delete(image)
                deleted_count += 1
            
            db.commit()
            
            return schemas.StandardResponse(
                success=True,
                data={
                    "category_name": category_name,
                    "action": "delete",
                    "deleted_images_count": deleted_count
                },
                message=f"分类及其下的 {deleted_count} 张图片已全部删除"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分类失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")
