import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from contextlib import asynccontextmanager
from fastapi.responses import JSONResponse
from app.model import models,schemas
from app.model import models,schemas
from app.database import database
from fastapi.middleware.cors import CORSMiddleware
from pydantic import ValidationError
from fastapi.exceptions import RequestValidationError
from app.model.utils import HourliveException

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

"""
业务数据库路由
"""
from app.routers.biz_routes import user_api_routers

app.include_router(user_api_routers.router,prefix="/user")

from app.routers.biz_routes import test_api_routers
app.include_router(test_api_routers.router,prefix="/test")

from app.routers.biz_routes import oss_api_routes
app.include_router(oss_api_routes.router,prefix="/oss")

from app.routers.biz_routes import ai_api_routers
app.include_router(ai_api_routers.router,prefix="/ai")

from app.routers.biz_routes import siteconfig_api_routers
app.include_router(siteconfig_api_routers.router,prefix="/siteconfig")

"""
图片管理路由
"""
from app.routers.biz_routes import image_api_routers
app.include_router(image_api_routers.router, prefix="/image", tags=["图片管理"])

from app.routers.biz_routes import image_tag_api_routers
app.include_router(image_tag_api_routers.router, prefix="/image-tag", tags=["图片标签管理"])

from app.routers.biz_routes import image_category_api_routers
app.include_router(image_category_api_routers.router, prefix="/image-category", tags=["图片分类管理"])

"""
文章管理路由
"""
from app.routers.biz_routes import article_category_api_routers
app.include_router(article_category_api_routers.router, prefix="/article-category", tags=["文章分类管理"])

from app.routers.biz_routes import article_author_api_routers
app.include_router(article_author_api_routers.router, prefix="/article-author", tags=["文章作者管理"])

from app.routers.biz_routes import article_tag_api_routers
app.include_router(article_tag_api_routers.router, prefix="/article-tag", tags=["文章标签管理"])

from app.routers.biz_routes import article_api_routers
app.include_router(article_api_routers.router, prefix="/article", tags=["文章管理"])

from app.routers.biz_routes import public_article_api_routers
app.include_router(public_article_api_routers.router, prefix="/public-article", tags=["用户端文章查询"])


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=str(exc)).dict()
    )

@app.exception_handler(HourliveException)
async def hourlive_exception_handler(request: Request, exc: HourliveException):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=exc.message, status_code=exc.status_code, toast=exc.message).dict()
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=exc.detail, status_code=exc.status_code).dict()
    )

@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=str(exc)).dict()
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=str(exc)).dict()
    )

# 在启动时创建所有表
@asynccontextmanager
async def lifespan(app: FastAPI):
    models.Base.metadata.create_all(bind=database.engine)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=9000)
